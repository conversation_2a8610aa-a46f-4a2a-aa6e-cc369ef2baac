// TECH-BROWSER - Servo Engine Integration
// Phase 1: Direct Servo integration for ultra-modern web rendering

use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use log::{info, warn, error};
use serde::{Deserialize, Serialize};

// Servo imports (will be added progressively)
use url::Url;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServoTab {
    pub id: String,
    pub url: String,
    pub title: String,
    pub is_loading: bool,
    pub can_go_back: bool,
    pub can_go_forward: bool,
}

#[derive(Debug)]
pub struct ServoEngine {
    tabs: Arc<Mutex<HashMap<String, ServoTab>>>,
    active_tab_id: Arc<Mutex<Option<String>>>,
    // Servo instances will be added here
}

impl Default for ServoEngine {
    fn default() -> Self {
        Self::new()
    }
}

impl ServoEngine {
    pub fn new() -> Self {
        info!("🚀 Initializing Servo Engine for TECH-BROWSER");
        
        let mut tabs = HashMap::new();
        let initial_tab_id = uuid::Uuid::new_v4().to_string();
        
        // Create initial tab with Servo
        let initial_tab = ServoTab {
            id: initial_tab_id.clone(),
            url: "tech://start".to_string(),
            title: "TECH-BROWSER".to_string(),
            is_loading: false,
            can_go_back: false,
            can_go_forward: false,
        };
        
        tabs.insert(initial_tab_id.clone(), initial_tab);
        
        Self {
            tabs: Arc::new(Mutex::new(tabs)),
            active_tab_id: Arc::new(Mutex::new(Some(initial_tab_id))),
        }
    }
    
    pub fn create_tab(&self, url: Option<String>) -> Result<String, String> {
        let tab_id = uuid::Uuid::new_v4().to_string();
        let url = url.unwrap_or_else(|| "tech://start".to_string());
        
        info!("📑 Creating new Servo tab: {} -> {}", tab_id, url);
        
        // Validate URL
        let validated_url = self.validate_url(&url)?;
        
        let tab = ServoTab {
            id: tab_id.clone(),
            url: validated_url.clone(),
            title: "Chargement...".to_string(),
            is_loading: true,
            can_go_back: false,
            can_go_forward: false,
        };
        
        // Add tab
        if let Ok(mut tabs) = self.tabs.lock() {
            tabs.insert(tab_id.clone(), tab);
        } else {
            return Err("Failed to lock tabs".to_string());
        }
        
        // Set as active tab
        if let Ok(mut active_tab_id) = self.active_tab_id.lock() {
            *active_tab_id = Some(tab_id.clone());
        }
        
        // Initialize Servo instance for this tab
        self.init_servo_instance(&tab_id, &validated_url)?;
        
        Ok(tab_id)
    }
    
    pub fn navigate(&self, tab_id: &str, url: &str) -> Result<(), String> {
        info!("🌐 Servo navigation: {} -> {}", tab_id, url);
        
        // Validate URL
        let validated_url = self.validate_url(url)?;
        
        // Update tab state
        if let Ok(mut tabs) = self.tabs.lock() {
            if let Some(tab) = tabs.get_mut(tab_id) {
                tab.url = validated_url.clone();
                tab.is_loading = true;
                tab.title = "Chargement...".to_string();
            } else {
                return Err("Tab not found".to_string());
            }
        } else {
            return Err("Failed to lock tabs".to_string());
        }
        
        // Navigate Servo instance
        self.servo_navigate(&tab_id, &validated_url)?;
        
        Ok(())
    }
    
    pub fn close_tab(&self, tab_id: &str) -> Result<(), String> {
        if let Ok(mut tabs) = self.tabs.lock() {
            if tabs.len() <= 1 {
                return Err("Cannot close last tab".to_string());
            }
            
            // Cleanup Servo instance
            self.cleanup_servo_instance(tab_id)?;
            
            tabs.remove(tab_id);
            
            // If this was the active tab, switch to another
            if let Ok(mut active_tab_id) = self.active_tab_id.lock() {
                if active_tab_id.as_ref() == Some(&tab_id.to_string()) {
                    *active_tab_id = tabs.keys().next().map(|k| k.clone());
                }
            }
            
            info!("❌ Closed Servo tab: {}", tab_id);
            Ok(())
        } else {
            Err("Failed to lock tabs".to_string())
        }
    }
    
    pub fn go_back(&self, tab_id: &str) -> Result<(), String> {
        info!("⬅️ Servo go back: {}", tab_id);
        
        // Check if can go back
        if let Ok(tabs) = self.tabs.lock() {
            if let Some(tab) = tabs.get(tab_id) {
                if !tab.can_go_back {
                    return Err("Cannot go back".to_string());
                }
            } else {
                return Err("Tab not found".to_string());
            }
        }
        
        // Execute Servo go back
        self.servo_go_back(tab_id)?;
        
        Ok(())
    }
    
    pub fn go_forward(&self, tab_id: &str) -> Result<(), String> {
        info!("➡️ Servo go forward: {}", tab_id);
        
        // Check if can go forward
        if let Ok(tabs) = self.tabs.lock() {
            if let Some(tab) = tabs.get(tab_id) {
                if !tab.can_go_forward {
                    return Err("Cannot go forward".to_string());
                }
            } else {
                return Err("Tab not found".to_string());
            }
        }
        
        // Execute Servo go forward
        self.servo_go_forward(tab_id)?;
        
        Ok(())
    }
    
    pub fn reload(&self, tab_id: &str) -> Result<(), String> {
        info!("🔄 Servo reload: {}", tab_id);
        
        if let Ok(tabs) = self.tabs.lock() {
            if let Some(tab) = tabs.get(tab_id) {
                let url = tab.url.clone();
                drop(tabs); // Release lock
                return self.navigate(tab_id, &url);
            }
        }
        
        Err("Tab not found".to_string())
    }
    
    pub fn get_tabs(&self) -> Vec<ServoTab> {
        if let Ok(tabs) = self.tabs.lock() {
            tabs.values().cloned().collect()
        } else {
            vec![]
        }
    }
    
    pub fn get_active_tab_id(&self) -> Option<String> {
        if let Ok(active_tab_id) = self.active_tab_id.lock() {
            active_tab_id.clone()
        } else {
            None
        }
    }
    
    pub fn set_active_tab(&self, tab_id: &str) -> Result<(), String> {
        if let Ok(tabs) = self.tabs.lock() {
            if tabs.contains_key(tab_id) {
                if let Ok(mut active_tab_id) = self.active_tab_id.lock() {
                    *active_tab_id = Some(tab_id.to_string());
                    info!("🎯 Switched to Servo tab: {}", tab_id);
                    return Ok(());
                }
            }
        }
        
        Err("Tab not found".to_string())
    }
    
    // Private methods for Servo integration
    
    fn validate_url(&self, url: &str) -> Result<String, String> {
        // Handle special URLs
        if url.starts_with("tech://") {
            return Ok(url.to_string());
        }
        
        // Add protocol if missing
        let url = if !url.starts_with("http://") && !url.starts_with("https://") {
            if url.contains('.') && !url.contains(' ') {
                format!("https://{}", url)
            } else {
                // Search query
                format!("https://www.google.com/search?q={}", urlencoding::encode(url))
            }
        } else {
            url.to_string()
        };
        
        // Validate URL format
        match Url::parse(&url) {
            Ok(_) => Ok(url),
            Err(_) => Err("Invalid URL format".to_string()),
        }
    }
    
    fn init_servo_instance(&self, tab_id: &str, url: &str) -> Result<(), String> {
        info!("🔧 Initializing Servo instance for tab: {}", tab_id);
        
        // TODO: Initialize actual Servo instance
        // For now, simulate initialization
        
        // Simulate page load completion
        self.simulate_servo_load(tab_id, url);
        
        Ok(())
    }
    
    fn servo_navigate(&self, tab_id: &str, url: &str) -> Result<(), String> {
        info!("🌐 Servo navigate: {} -> {}", tab_id, url);
        
        // TODO: Execute actual Servo navigation
        // For now, simulate navigation
        
        // Simulate page load
        self.simulate_servo_load(tab_id, url);
        
        Ok(())
    }
    
    fn servo_go_back(&self, _tab_id: &str) -> Result<(), String> {
        // TODO: Implement Servo go back
        info!("⬅️ Servo go back executed");
        Ok(())
    }
    
    fn servo_go_forward(&self, _tab_id: &str) -> Result<(), String> {
        // TODO: Implement Servo go forward
        info!("➡️ Servo go forward executed");
        Ok(())
    }
    
    fn cleanup_servo_instance(&self, tab_id: &str) -> Result<(), String> {
        info!("🧹 Cleaning up Servo instance: {}", tab_id);
        
        // TODO: Cleanup actual Servo instance
        
        Ok(())
    }
    
    fn simulate_servo_load(&self, tab_id: &str, url: &str) {
        let tabs = self.tabs.clone();
        let tab_id = tab_id.to_string();
        let url = url.to_string();
        
        // Simulate async Servo page loading
        tokio::spawn(async move {
            tokio::time::sleep(tokio::time::Duration::from_millis(800)).await;
            
            if let Ok(mut tabs) = tabs.lock() {
                if let Some(tab) = tabs.get_mut(&tab_id) {
                    tab.is_loading = false;
                    tab.title = extract_title_from_url(&url);
                    tab.can_go_back = true; // Simulate history
                    tab.can_go_forward = false;
                }
            }
            
            info!("✅ Servo page loaded: {} -> {}", tab_id, url);
        });
    }
}

fn extract_title_from_url(url: &str) -> String {
    if url.starts_with("tech://start") {
        return "TECH-BROWSER".to_string();
    }
    
    if let Ok(parsed_url) = Url::parse(url) {
        if let Some(host) = parsed_url.host_str() {
            // Extract domain name
            let domain = host.replace("www.", "");
            let parts: Vec<&str> = domain.split('.').collect();
            if !parts.is_empty() {
                return format!("{} - Servo", parts[0].to_uppercase());
            }
        }
    }
    
    "Site Web - Servo".to_string()
}
