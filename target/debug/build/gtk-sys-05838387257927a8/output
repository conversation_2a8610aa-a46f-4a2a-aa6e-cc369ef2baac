cargo:rerun-if-env-changed=GTK+_3.0_NO_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=SYSROOT
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rustc-link-lib=gtk-3
cargo:rustc-link-lib=gdk-3
cargo:rustc-link-lib=z
cargo:rustc-link-lib=pangocairo-1.0
cargo:rustc-link-lib=pango-1.0
cargo:rustc-link-lib=harfbuzz
cargo:rustc-link-lib=atk-1.0
cargo:rustc-link-lib=cairo-gobject
cargo:rustc-link-lib=cairo
cargo:rustc-link-lib=gdk_pixbuf-2.0
cargo:rustc-link-lib=gio-2.0
cargo:rustc-link-lib=gobject-2.0
cargo:rustc-link-lib=glib-2.0
cargo:include=/usr/include/gtk-3.0:/usr/include/pango-1.0:/usr/include:/usr/include/glib-2.0:/usr/lib/x86_64-linux-gnu/glib-2.0/include:/usr/include/harfbuzz:/usr/include/freetype2:/usr/include/libpng16:/usr/include/libmount:/usr/include/blkid:/usr/include/fribidi:/usr/include/cairo:/usr/include/pixman-1:/usr/include/gdk-pixbuf-2.0:/usr/include/x86_64-linux-gnu:/usr/include/webp:/usr/include/gio-unix-2.0:/usr/include/atk-1.0:/usr/include/at-spi2-atk/2.0:/usr/include/at-spi-2.0:/usr/include/dbus-1.0:/usr/lib/x86_64-linux-gnu/dbus-1.0/include
cargo:rerun-if-env-changed=SYSTEM_DEPS_BUILD_INTERNAL
cargo:rerun-if-env-changed=SYSTEM_DEPS_LINK
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_LIB
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_LIB_FRAMEWORK
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_SEARCH_NATIVE
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_SEARCH_FRAMEWORK
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_INCLUDE
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_NO_PKG_CONFIG
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_BUILD_INTERNAL
cargo:rerun-if-env-changed=SYSTEM_DEPS_GTK_3_0_LINK

cargo:rustc-cfg=system_deps_have_gtk_3_0
