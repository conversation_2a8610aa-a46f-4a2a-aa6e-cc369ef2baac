// Copyright (c) 2025 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool and should not edited
// by hand. See the translator.README.txt file in the tools directory for
// more information.
//
// $hash=c79c0b685306bfc5de847142f9c0abb36e88c891$
//

#ifndef CEF_INCLUDE_CAPI_CEF_ORIGIN_WHITELIST_CAPI_H_
#define CEF_INCLUDE_CAPI_CEF_ORIGIN_WHITELIST_CAPI_H_
#pragma once

#if defined(BUILDING_CEF_SHARED)
#error This file cannot be included DLL-side
#endif

#include "include/capi/cef_base_capi.h"

#ifdef __cplusplus
extern "C" {
#endif

///
/// Add an entry to the cross-origin access whitelist.
///
/// The same-origin policy restricts how scripts hosted from different origins
/// (scheme + domain + port) can communicate. By default, scripts can only
/// access resources with the same origin. Scripts hosted on the HTTP and HTTPS
/// schemes (but no other schemes) can use the "Access-Control-Allow-Origin"
/// header to allow cross-origin requests. For example,
/// https://source.example.com can make XMLHttpRequest requests on
/// http://target.example.com if the http://target.example.com request returns
/// an "Access-Control-Allow-Origin: https://source.example.com" response
/// header.
///
/// Scripts in separate frames or iframes and hosted from the same protocol and
/// domain suffix can execute cross-origin JavaScript if both pages set the
/// document.domain value to the same domain suffix. For example,
/// scheme://foo.example.com and scheme://bar.example.com can communicate using
/// JavaScript if both domains set document.domain="example.com".
///
/// This function is used to allow access to origins that would otherwise
/// violate the same-origin policy. Scripts hosted underneath the fully
/// qualified |source_origin| URL (like http://www.example.com) will be allowed
/// access to all resources hosted on the specified |target_protocol| and
/// |target_domain|. If |target_domain| is non-NULL and
/// |allow_target_subdomains| is false (0) only exact domain matches will be
/// allowed. If |target_domain| contains a top- level domain component (like
/// "example.com") and |allow_target_subdomains| is true (1) sub-domain matches
/// will be allowed. If |target_domain| is NULL and |allow_target_subdomains| if
/// true (1) all domains and IP addresses will be allowed.
///
/// This function cannot be used to bypass the restrictions on local or display
/// isolated schemes. See the comments on CefRegisterCustomScheme for more
/// information.
///
/// This function may be called on any thread. Returns false (0) if
/// |source_origin| is invalid or the whitelist cannot be accessed.
///
CEF_EXPORT int cef_add_cross_origin_whitelist_entry(
    const cef_string_t* source_origin,
    const cef_string_t* target_protocol,
    const cef_string_t* target_domain,
    int allow_target_subdomains);

///
/// Remove an entry from the cross-origin access whitelist. Returns false (0) if
/// |source_origin| is invalid or the whitelist cannot be accessed.
///
CEF_EXPORT int cef_remove_cross_origin_whitelist_entry(
    const cef_string_t* source_origin,
    const cef_string_t* target_protocol,
    const cef_string_t* target_domain,
    int allow_target_subdomains);

///
/// Remove all entries from the cross-origin access whitelist. Returns false (0)
/// if the whitelist cannot be accessed.
///
CEF_EXPORT int cef_clear_cross_origin_whitelist(void);

#ifdef __cplusplus
}
#endif

#endif  // CEF_INCLUDE_CAPI_CEF_ORIGIN_WHITELIST_CAPI_H_
