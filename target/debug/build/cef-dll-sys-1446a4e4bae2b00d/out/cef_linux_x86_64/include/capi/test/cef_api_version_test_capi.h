// Copyright (c) 2025 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool and should not edited
// by hand. See the translator.README.txt file in the tools directory for
// more information.
//
// $hash=ed382228145472406bd83e3de71f7b0d75026caf$
//

#ifndef CEF_INCLUDE_CAPI_TEST_CEF_API_VERSION_TEST_CAPI_H_
#define CEF_INCLUDE_CAPI_TEST_CEF_API_VERSION_TEST_CAPI_H_
#pragma once

#if defined(BUILDING_CEF_SHARED)
#error This file cannot be included DLL-side
#endif

#if !defined(WRAPPING_CEF_SHARED) && !defined(UNIT_TEST)
#error This file can be included for unit tests only
#endif

#include "include/capi/cef_base_capi.h"
#include "include/cef_api_hash.h"

#ifdef __cplusplus
extern "C" {
#endif

#if CEF_API_REMOVED(13302)
struct _cef_api_version_test_ref_ptr_client_child_t;
#endif
#if CEF_API_ADDED(13302)
struct _cef_api_version_test_ref_ptr_client_child_v2_t;
#endif
struct _cef_api_version_test_ref_ptr_client_t;
struct _cef_api_version_test_ref_ptr_library_child_t;
struct _cef_api_version_test_ref_ptr_library_t;
#if CEF_API_REMOVED(13302)
struct _cef_api_version_test_scoped_client_child_t;
#endif
#if CEF_API_ADDED(13302)
struct _cef_api_version_test_scoped_client_child_v2_t;
#endif
struct _cef_api_version_test_scoped_client_t;
struct _cef_api_version_test_scoped_library_child_t;
struct _cef_api_version_test_scoped_library_t;

///
/// Structure for testing versioned object transfer.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Return an new library-side object.
  ///
  struct _cef_api_version_test_ref_ptr_library_t*(
      CEF_CALLBACK* get_ref_ptr_library)(struct _cef_api_version_test_t* self,
                                         int val);

  ///
  /// Set an object. Returns the value from
  /// cef_api_version_test_ref_ptr_library_t::get_value(). This tests input and
  /// execution of a library-side object type.
  ///
  int(CEF_CALLBACK* set_ref_ptr_library)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_library_t* val);

  ///
  /// Set an object. Returns the object passed in. This tests input and output
  /// of a library-side object type.
  ///
  struct _cef_api_version_test_ref_ptr_library_t*(
      CEF_CALLBACK* set_ref_ptr_library_and_return)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_library_t* val);

  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_ref_ptr_library_t::get_value(). This tests input of a
  /// library- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_ref_ptr_library)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_library_child_t* val);

  ///
  /// Set a child object. Returns the object as the parent type. This tests
  /// input of a library-side child object type and return as the parent type.
  ///
  struct _cef_api_version_test_ref_ptr_library_t*(
      CEF_CALLBACK* set_child_ref_ptr_library_and_return_parent)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_library_child_t* val);

  ///
  /// Set an object list vlaue.
  ///
  int(CEF_CALLBACK* set_ref_ptr_library_list)(
      struct _cef_api_version_test_t* self,
      size_t valCount,
      struct _cef_api_version_test_ref_ptr_library_t* const* val,
      int val1,
      int val2);

  ///
  /// Return an object list value by out-param.
  ///
  int(CEF_CALLBACK* get_ref_ptr_library_list_by_ref)(
      struct _cef_api_version_test_t* self,
      size_t* valCount,
      struct _cef_api_version_test_ref_ptr_library_t** val,
      int val1,
      int val2);

  ///
  /// Return the number of object that will be output above.
  ///
  size_t(CEF_CALLBACK* get_ref_ptr_library_list_size)(
      struct _cef_api_version_test_t* self);

  ///
  /// Set an object. Returns the value from
  /// cef_api_version_test_ref_ptr_client_t::get_value(). This tests input and
  /// execution of a client-side object type.
  ///
  int(CEF_CALLBACK* set_ref_ptr_client)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_client_t* val);

  ///
  /// Set an object. Returns the handler passed in. This tests input and output
  /// of a client-side object type.
  ///
  struct _cef_api_version_test_ref_ptr_client_t*(
      CEF_CALLBACK* set_ref_ptr_client_and_return)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_client_t* val);

#if CEF_API_REMOVED(13302)
  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_ref_ptr_client_t::get_value(). This tests input of a
  /// client- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_ref_ptr_client)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_client_child_t* val);
#else
  uintptr_t set_child_ref_ptr_client_removed;
#endif

#if CEF_API_REMOVED(13302)
  ///
  /// Set a child object. Returns the object as the parent type. This tests
  /// input of a client-side child object type and return as the parent type.
  ///
  struct _cef_api_version_test_ref_ptr_client_t*(
      CEF_CALLBACK* set_child_ref_ptr_client_and_return_parent)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_client_child_t* val);
#else
  uintptr_t set_child_ref_ptr_client_and_return_parent_removed;
#endif

  ///
  /// Set an object list vlaue.
  ///
  int(CEF_CALLBACK* set_ref_ptr_client_list)(
      struct _cef_api_version_test_t* self,
      size_t valCount,
      struct _cef_api_version_test_ref_ptr_client_t* const* val,
      int val1,
      int val2);

  ///
  /// Return an object list value by out-param.
  ///
  int(CEF_CALLBACK* get_ref_ptr_client_list_by_ref)(
      struct _cef_api_version_test_t* self,
      size_t* valCount,
      struct _cef_api_version_test_ref_ptr_client_t** val,
      struct _cef_api_version_test_ref_ptr_client_t* val1,
      struct _cef_api_version_test_ref_ptr_client_t* val2);

  ///
  /// Return the number of object that will be output above.
  ///
  size_t(CEF_CALLBACK* get_ref_ptr_client_list_size)(
      struct _cef_api_version_test_t* self);

  ///
  /// Return an new library-side object.
  ///
  struct _cef_api_version_test_scoped_library_t*(
      CEF_CALLBACK* get_own_ptr_library)(struct _cef_api_version_test_t* self,
                                         int val);

  ///
  /// Set an object. Returns the value from
  /// cef_api_version_test_scoped_library_t::get_value(). This tests input and
  /// execution of a library-side object type.
  ///
  int(CEF_CALLBACK* set_own_ptr_library)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_library_t* val);

  ///
  /// Set an object. Returns the object passed in. This tests input and output
  /// of a library-side object type.
  ///
  struct _cef_api_version_test_scoped_library_t*(
      CEF_CALLBACK* set_own_ptr_library_and_return)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_library_t* val);

  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_scoped_library_t::get_value(). This tests input of a
  /// library- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_own_ptr_library)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_library_child_t* val);

  ///
  /// Set a child object. Returns the object as the parent type. This tests
  /// input of a library-side child object type and return as the parent type.
  ///
  struct _cef_api_version_test_scoped_library_t*(
      CEF_CALLBACK* set_child_own_ptr_library_and_return_parent)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_library_child_t* val);

  ///
  /// Set an object. Returns the value from
  /// cef_api_version_test_scoped_client_t::get_value(). This tests input and
  /// execution of a client-side object type.
  ///
  int(CEF_CALLBACK* set_own_ptr_client)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_t* val);

  ///
  /// Set an object. Returns the handler passed in. This tests input and output
  /// of a client-side object type.
  ///
  struct _cef_api_version_test_scoped_client_t*(
      CEF_CALLBACK* set_own_ptr_client_and_return)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_t* val);

#if CEF_API_REMOVED(13302)
  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_scoped_client_t::get_value(). This tests input of a
  /// client- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_own_ptr_client)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_child_t* val);
#else
  uintptr_t set_child_own_ptr_client_removed;
#endif

#if CEF_API_REMOVED(13302)
  ///
  /// Set a child object. Returns the object as the parent type. This tests
  /// input of a client-side child object type and return as the parent type.
  ///
  struct _cef_api_version_test_scoped_client_t*(
      CEF_CALLBACK* set_child_own_ptr_client_and_return_parent)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_child_t* val);
#else
  uintptr_t set_child_own_ptr_client_and_return_parent_removed;
#endif

  ///
  /// Set an object. Returns the value from
  /// cef_api_version_test_scoped_library_t::get_value(). This tests input and
  /// execution of a library-side object type.
  ///
  int(CEF_CALLBACK* set_raw_ptr_library)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_library_t* val);

  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_scoped_library_t::get_value(). This tests input of a
  /// library- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_raw_ptr_library)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_library_child_t* val);

  ///
  /// Set an object list vlaue.
  ///
  int(CEF_CALLBACK* set_raw_ptr_library_list)(
      struct _cef_api_version_test_t* self,
      size_t valCount,
      struct _cef_api_version_test_scoped_library_t* const* val,
      int val1,
      int val2);

  ///
  /// Set an object. Returns the value from
  /// cef_api_version_test_scoped_client_t::get_value(). This tests input and
  /// execution of a client-side object type.
  ///
  int(CEF_CALLBACK* set_raw_ptr_client)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_t* val);

#if CEF_API_REMOVED(13302)
  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_scoped_client_t::get_value(). This tests input of a
  /// client- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_raw_ptr_client)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_child_t* val);
#else
  uintptr_t set_child_raw_ptr_client_removed;
#endif

  ///
  /// Set an object list vlaue.
  ///
  int(CEF_CALLBACK* set_raw_ptr_client_list)(
      struct _cef_api_version_test_t* self,
      size_t valCount,
      struct _cef_api_version_test_scoped_client_t* const* val,
      int val1,
      int val2);

#if CEF_API_ADDED(13302)
  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_ref_ptr_client_t::get_value(). This tests input of a
  /// client- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_ref_ptr_client_v2)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_client_child_v2_t* val);
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Set a child object. Returns the object as the parent type. This tests
  /// input of a client-side child object type and return as the parent type.
  ///
  struct _cef_api_version_test_ref_ptr_client_t*(
      CEF_CALLBACK* set_child_ref_ptr_client_and_return_parent_v2)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_ref_ptr_client_child_v2_t* val);
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_scoped_client_t::get_value(). This tests input of a
  /// client- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_own_ptr_client_v2)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_child_v2_t* val);
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Set a child object. Returns the object as the parent type. This tests
  /// input of a client-side child object type and return as the parent type.
  ///
  struct _cef_api_version_test_scoped_client_t*(
      CEF_CALLBACK* set_child_own_ptr_client_and_return_parent_v2)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_child_v2_t* val);
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Set a child object. Returns the value from
  /// cef_api_version_test_scoped_client_t::get_value(). This tests input of a
  /// client- side child object type and execution as the parent type.
  ///
  int(CEF_CALLBACK* set_child_raw_ptr_client_v2)(
      struct _cef_api_version_test_t* self,
      struct _cef_api_version_test_scoped_client_child_v2_t* val);
#endif
} cef_api_version_test_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_t* cef_api_version_test_create(void);

///
/// Library-side test object for RefPtr.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_ref_ptr_library_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Return a legacy value.
  ///
  int(CEF_CALLBACK* get_value_legacy)(
      struct _cef_api_version_test_ref_ptr_library_t* self);

  ///
  /// Set a legacy value.
  ///
  void(CEF_CALLBACK* set_value_legacy)(
      struct _cef_api_version_test_ref_ptr_library_t* self,
      int value);

#if CEF_API_REMOVED(13301)
  ///
  /// Return a value. This is replaced by GetValueV1 in version 13301.
  ///
  int(CEF_CALLBACK* get_value)(
      struct _cef_api_version_test_ref_ptr_library_t* self);
#else
  uintptr_t get_value_removed;
#endif

#if CEF_API_REMOVED(13301)
  ///
  /// Set a value. This is replaced by SetValueV1 in version 13301.
  ///
  void(CEF_CALLBACK* set_value)(
      struct _cef_api_version_test_ref_ptr_library_t* self,
      int value);
#else
  uintptr_t set_value_removed;
#endif

#if CEF_API_RANGE(13301, 13302)
  ///
  /// Return a value (V1). This replaces GetValue in version 13301 and is
  /// replaced by cef_value_tV2 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v1)(
      struct _cef_api_version_test_ref_ptr_library_t* self);
#elif CEF_API_ADDED(13302)
  uintptr_t get_value_v1_removed;
#endif

#if CEF_API_RANGE(13301, 13302)
  ///
  /// Set a value (V1). This replaces SetValue in version 13301 and is replaced
  /// by SefValueV2 in version 13302.
  ///
  void(CEF_CALLBACK* set_value_v1)(
      struct _cef_api_version_test_ref_ptr_library_t* self,
      int value);
#elif CEF_API_ADDED(13302)
  uintptr_t set_value_v1_removed;
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Return a value (V2). This replaces GetValueV1 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v2)(
      struct _cef_api_version_test_ref_ptr_library_t* self);
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Set a value (V2). This replaces SetValueV1 in version 13302.
  ///
  void(CEF_CALLBACK* set_value_v2)(
      struct _cef_api_version_test_ref_ptr_library_t* self,
      int value);
#endif

#if CEF_API_ADDED(CEF_EXPERIMENTAL)
  ///
  /// Return an experimental value.
  ///
  int(CEF_CALLBACK* get_value_exp)(
      struct _cef_api_version_test_ref_ptr_library_t* self);
#endif

#if CEF_API_ADDED(CEF_EXPERIMENTAL)
  ///
  /// Set an experimental value.
  ///
  void(CEF_CALLBACK* set_value_exp)(
      struct _cef_api_version_test_ref_ptr_library_t* self,
      int value);
#endif
} cef_api_version_test_ref_ptr_library_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_t*
cef_api_version_test_ref_ptr_library_create(void);

#if CEF_API_ADDED(13301)
///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_t*
cef_api_version_test_ref_ptr_library_create_with_default(int value);
#endif

///
/// Library-side child test object for RefPtr.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_ref_ptr_library_child_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_ref_ptr_library_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_t* self);

  ///
  /// Set a value.
  ///
  void(CEF_CALLBACK* set_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_t* self,
      int value);
} cef_api_version_test_ref_ptr_library_child_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_child_t*
cef_api_version_test_ref_ptr_library_child_create(void);

#if CEF_API_ADDED(13301)
///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_child_t*
cef_api_version_test_ref_ptr_library_child_create_with_default(int value,
                                                               int other_value);
#endif

#if CEF_API_REMOVED(13301)

///
/// Another library-side child test object for RefPtr. This is replaced by
/// cef_api_version_test_ref_ptr_library_child_child_v1_t in version 13301.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_ref_ptr_library_child_child_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_ref_ptr_library_child_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_child_t* self);

  ///
  /// Set a value.
  ///
  void(CEF_CALLBACK* set_other_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_child_t* self,
      int value);
} cef_api_version_test_ref_ptr_library_child_child_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_child_child_t*
cef_api_version_test_ref_ptr_library_child_child_create(void);

#endif  // CEF_API_REMOVED(13301)

#if CEF_API_RANGE(13301, 13302)

///
/// Another library-side child test object for RefPtr. This replaces
/// cef_api_version_test_ref_ptr_library_child_child_t in version 13301 and is
/// replaced by cef_api_version_test_ref_ptr_library_child_child_v2_t in version
/// 13302.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_ref_ptr_library_child_child_v1_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_ref_ptr_library_child_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_child_v1_t* self);

  ///
  /// Set a value.
  ///
  void(CEF_CALLBACK* set_other_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_child_v1_t* self,
      int value);
} cef_api_version_test_ref_ptr_library_child_child_v1_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_child_child_v1_t*
cef_api_version_test_ref_ptr_library_child_child_v1_create(void);

///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_child_child_v1_t*
cef_api_version_test_ref_ptr_library_child_child_v1_create_with_default(
    int value,
    int other_value,
    int other_other_value);

#endif  // CEF_API_RANGE(13301, 13302)

#if CEF_API_ADDED(13302)

///
/// Another library-side child test object for RefPtr. This replaces
/// cef_api_version_test_ref_ptr_library_child_child_v1_t in version 13302.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_ref_ptr_library_child_child_v2_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_ref_ptr_library_child_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_child_v2_t* self);

  ///
  /// Set a value (v3).
  ///
  void(CEF_CALLBACK* set_other_other_value)(
      struct _cef_api_version_test_ref_ptr_library_child_child_v2_t* self,
      int value);
} cef_api_version_test_ref_ptr_library_child_child_v2_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_child_child_v2_t*
cef_api_version_test_ref_ptr_library_child_child_v2_create(void);

///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_ref_ptr_library_child_child_v2_t*
cef_api_version_test_ref_ptr_library_child_child_v2_create_with_default(
    int value,
    int other_value,
    int other_other_value);

#endif  // CEF_API_ADDED(13302)

///
/// Client-side test object for RefPtr.
///
/// NOTE: This struct is allocated client-side.
///
typedef struct _cef_api_version_test_ref_ptr_client_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Return a legacy value.
  ///
  int(CEF_CALLBACK* get_value_legacy)(
      struct _cef_api_version_test_ref_ptr_client_t* self);

#if CEF_API_REMOVED(13301)
  ///
  /// Return a value. This is replaced with GetValueV1 in version 13301.
  ///
  int(CEF_CALLBACK* get_value)(
      struct _cef_api_version_test_ref_ptr_client_t* self);
#else
  uintptr_t get_value_removed;
#endif

#if CEF_API_RANGE(13301, 13302)
  ///
  /// Return a value (V1). This replaces GetValue in version 13301 and is
  /// replaced with GetValueV2 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v1)(
      struct _cef_api_version_test_ref_ptr_client_t* self);
#elif CEF_API_ADDED(13302)
  uintptr_t get_value_v1_removed;
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Return a value (V2). This replaces GetValueV1 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v2)(
      struct _cef_api_version_test_ref_ptr_client_t* self);
#endif

#if CEF_API_ADDED(CEF_EXPERIMENTAL)
  ///
  /// Return an experimental value.
  ///
  int(CEF_CALLBACK* get_value_exp)(
      struct _cef_api_version_test_ref_ptr_client_t* self);
#endif
} cef_api_version_test_ref_ptr_client_t;

#if CEF_API_REMOVED(13302)

///
/// Client-side child test object for RefPtr. This is replaced with
/// cef_api_version_test_ref_ptr_client_child_v2_t in version 13302.
///
/// NOTE: This struct is allocated client-side.
///
typedef struct _cef_api_version_test_ref_ptr_client_child_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_ref_ptr_client_t base;

#if CEF_API_REMOVED(13301)
  ///
  /// Return a value. This is replaced with GetOtherValueV1 in version 13301.
  ///
  int(CEF_CALLBACK* get_other_value)(
      struct _cef_api_version_test_ref_ptr_client_child_t* self);
#else
  uintptr_t get_other_value_removed;
#endif

#if CEF_API_ADDED(13301)
  ///
  /// Return a value (V1). This replaces GetOtherValue in version 13301.
  ///
  int(CEF_CALLBACK* get_other_value_v1)(
      struct _cef_api_version_test_ref_ptr_client_child_t* self);
#endif
} cef_api_version_test_ref_ptr_client_child_t;

#endif  // CEF_API_REMOVED(13302)

#if CEF_API_ADDED(13302)

///
/// Client-side child test object for RefPtr. This replaces
/// cef_api_version_test_ref_ptr_client_child_t in version 13302.
///
/// NOTE: This struct is allocated client-side.
///
typedef struct _cef_api_version_test_ref_ptr_client_child_v2_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_ref_ptr_client_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_value)(
      struct _cef_api_version_test_ref_ptr_client_child_v2_t* self);

#if CEF_API_ADDED(13303)
  ///
  /// Return another value.
  ///
  int(CEF_CALLBACK* get_another_value)(
      struct _cef_api_version_test_ref_ptr_client_child_v2_t* self);
#endif
} cef_api_version_test_ref_ptr_client_child_v2_t;

#endif  // CEF_API_ADDED(13302)

///
/// Library-side test object for OwnPtr/RawPtr.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_scoped_library_t {
  ///
  /// Base structure.
  ///
  cef_base_scoped_t base;

  ///
  /// Return a legacy value.
  ///
  int(CEF_CALLBACK* get_value_legacy)(
      struct _cef_api_version_test_scoped_library_t* self);

  ///
  /// Set a legacy value.
  ///
  void(CEF_CALLBACK* set_value_legacy)(
      struct _cef_api_version_test_scoped_library_t* self,
      int value);

#if CEF_API_REMOVED(13301)
  ///
  /// Return a value. This is replaced by GetValueV1 in version 13301.
  ///
  int(CEF_CALLBACK* get_value)(
      struct _cef_api_version_test_scoped_library_t* self);
#else
  uintptr_t get_value_removed;
#endif

#if CEF_API_REMOVED(13301)
  ///
  /// Set a value. This is replaced by SetValueV1 in version 13301.
  ///
  void(CEF_CALLBACK* set_value)(
      struct _cef_api_version_test_scoped_library_t* self,
      int value);
#else
  uintptr_t set_value_removed;
#endif

#if CEF_API_RANGE(13301, 13302)
  ///
  /// Return a value (V1). This replaces GetValue in version 13301 and is
  /// replaced by cef_value_tV2 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v1)(
      struct _cef_api_version_test_scoped_library_t* self);
#elif CEF_API_ADDED(13302)
  uintptr_t get_value_v1_removed;
#endif

#if CEF_API_RANGE(13301, 13302)
  ///
  /// Set a value (V1). This replaces SetValue in version 13301 and is replaced
  /// by SefValueV2 in version 13302.
  ///
  void(CEF_CALLBACK* set_value_v1)(
      struct _cef_api_version_test_scoped_library_t* self,
      int value);
#elif CEF_API_ADDED(13302)
  uintptr_t set_value_v1_removed;
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Return a value (V2). This replaces GetValueV1 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v2)(
      struct _cef_api_version_test_scoped_library_t* self);
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Set a value (V2). This replaces SetValueV1 in version 13302.
  ///
  void(CEF_CALLBACK* set_value_v2)(
      struct _cef_api_version_test_scoped_library_t* self,
      int value);
#endif

#if CEF_API_ADDED(CEF_EXPERIMENTAL)
  ///
  /// Return an experimental value.
  ///
  int(CEF_CALLBACK* get_value_exp)(
      struct _cef_api_version_test_scoped_library_t* self);
#endif

#if CEF_API_ADDED(CEF_EXPERIMENTAL)
  ///
  /// Set an experimental value.
  ///
  void(CEF_CALLBACK* set_value_exp)(
      struct _cef_api_version_test_scoped_library_t* self,
      int value);
#endif
} cef_api_version_test_scoped_library_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_scoped_library_t*
cef_api_version_test_scoped_library_create(void);

#if CEF_API_ADDED(13301)
///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_scoped_library_t*
cef_api_version_test_scoped_library_create_with_default(int value);
#endif

///
/// Library-side child test object for OwnPtr/RawPtr.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_scoped_library_child_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_scoped_library_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_value)(
      struct _cef_api_version_test_scoped_library_child_t* self);

  ///
  /// Set a value.
  ///
  void(CEF_CALLBACK* set_other_value)(
      struct _cef_api_version_test_scoped_library_child_t* self,
      int value);
} cef_api_version_test_scoped_library_child_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_scoped_library_child_t*
cef_api_version_test_scoped_library_child_create(void);

#if CEF_API_ADDED(13301)
///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_scoped_library_child_t*
cef_api_version_test_scoped_library_child_create_with_default(int value,
                                                              int other_value);
#endif

#if CEF_API_REMOVED(13301)

///
/// Another library-side child test object for OwnPtr/RawPtr. This is replaced
/// by cef_api_version_test_scoped_library_child_child_v1_t in version 13301.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_scoped_library_child_child_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_scoped_library_child_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_other_value)(
      struct _cef_api_version_test_scoped_library_child_child_t* self);

  ///
  /// Set a value.
  ///
  void(CEF_CALLBACK* set_other_other_value)(
      struct _cef_api_version_test_scoped_library_child_child_t* self,
      int value);
} cef_api_version_test_scoped_library_child_child_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_scoped_library_child_child_t*
cef_api_version_test_scoped_library_child_child_create(void);

#endif  // CEF_API_REMOVED(13301)

#if CEF_API_RANGE(13301, 13302)

///
/// Another library-side child test object for OwnPtr/RawPtr. This replaces
/// cef_api_version_test_scoped_library_child_child_t in version 13301 and is
/// replaced by cef_api_version_test_scoped_library_child_child_v2_t in version
/// 13302.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_scoped_library_child_child_v1_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_scoped_library_child_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_other_value)(
      struct _cef_api_version_test_scoped_library_child_child_v1_t* self);

  ///
  /// Set a value.
  ///
  void(CEF_CALLBACK* set_other_other_value)(
      struct _cef_api_version_test_scoped_library_child_child_v1_t* self,
      int value);
} cef_api_version_test_scoped_library_child_child_v1_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_scoped_library_child_child_v1_t*
cef_api_version_test_scoped_library_child_child_v1_create(void);

///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_scoped_library_child_child_v1_t*
cef_api_version_test_scoped_library_child_child_v1_create_with_default(
    int value,
    int other_value,
    int other_other_value);

#endif  // CEF_API_RANGE(13301, 13302)

#if CEF_API_ADDED(13302)

///
/// Another library-side child test object for OwnPtr/RawPtr. This replaces
/// cef_api_version_test_scoped_library_child_child_v1_t in version 13302.
///
/// NOTE: This struct is allocated DLL-side.
///
typedef struct _cef_api_version_test_scoped_library_child_child_v2_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_scoped_library_child_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_other_value)(
      struct _cef_api_version_test_scoped_library_child_child_v2_t* self);

  ///
  /// Set a value (v3).
  ///
  void(CEF_CALLBACK* set_other_other_value)(
      struct _cef_api_version_test_scoped_library_child_child_v2_t* self,
      int value);
} cef_api_version_test_scoped_library_child_child_v2_t;

///
/// Create the test object.
///
CEF_EXPORT cef_api_version_test_scoped_library_child_child_v2_t*
cef_api_version_test_scoped_library_child_child_v2_create(void);

///
/// Create the test object with default value.
///
CEF_EXPORT cef_api_version_test_scoped_library_child_child_v2_t*
cef_api_version_test_scoped_library_child_child_v2_create_with_default(
    int value,
    int other_value,
    int other_other_value);

#endif  // CEF_API_ADDED(13302)

///
/// Client-side test object for OwnPtr/RawPtr.
///
/// NOTE: This struct is allocated client-side.
///
typedef struct _cef_api_version_test_scoped_client_t {
  ///
  /// Base structure.
  ///
  cef_base_scoped_t base;

  ///
  /// Return a legacy value.
  ///
  int(CEF_CALLBACK* get_value_legacy)(
      struct _cef_api_version_test_scoped_client_t* self);

#if CEF_API_REMOVED(13301)
  ///
  /// Return a value. This is replaced with GetValueV1 in version 13301.
  ///
  int(CEF_CALLBACK* get_value)(
      struct _cef_api_version_test_scoped_client_t* self);
#else
  uintptr_t get_value_removed;
#endif

#if CEF_API_RANGE(13301, 13302)
  ///
  /// Return a value (V1). This replaces GetValue in version 13301 and is
  /// replaced with GetValueV2 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v1)(
      struct _cef_api_version_test_scoped_client_t* self);
#elif CEF_API_ADDED(13302)
  uintptr_t get_value_v1_removed;
#endif

#if CEF_API_ADDED(13302)
  ///
  /// Return a value (V2). This replaces GetValueV1 in version 13302.
  ///
  int(CEF_CALLBACK* get_value_v2)(
      struct _cef_api_version_test_scoped_client_t* self);
#endif

#if CEF_API_ADDED(CEF_EXPERIMENTAL)
  ///
  /// Return an experimental value.
  ///
  int(CEF_CALLBACK* get_value_exp)(
      struct _cef_api_version_test_scoped_client_t* self);
#endif
} cef_api_version_test_scoped_client_t;

#if CEF_API_REMOVED(13302)

///
/// Client-side child test object for OwnPtr/RawPtr. This is replaced with
/// cef_api_version_test_scoped_client_child_v2_t in version 13302.
///
/// NOTE: This struct is allocated client-side.
///
typedef struct _cef_api_version_test_scoped_client_child_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_scoped_client_t base;

#if CEF_API_REMOVED(13301)
  ///
  /// Return a value. This is replaced with GetOtherValueV1 in version 13301.
  ///
  int(CEF_CALLBACK* get_other_value)(
      struct _cef_api_version_test_scoped_client_child_t* self);
#else
  uintptr_t get_other_value_removed;
#endif

#if CEF_API_ADDED(13301)
  ///
  /// Return a value (V1). This replaces GetOtherValue in version 13301.
  ///
  int(CEF_CALLBACK* get_other_value_v1)(
      struct _cef_api_version_test_scoped_client_child_t* self);
#endif
} cef_api_version_test_scoped_client_child_t;

#endif  // CEF_API_REMOVED(13302)

#if CEF_API_ADDED(13302)

///
/// Client-side child test object for OwnPtr/RawPtr. This replaces
/// cef_api_version_test_scoped_client_child_t in version 13302.
///
/// NOTE: This struct is allocated client-side.
///
typedef struct _cef_api_version_test_scoped_client_child_v2_t {
  ///
  /// Base structure.
  ///
  cef_api_version_test_scoped_client_t base;

  ///
  /// Return a value.
  ///
  int(CEF_CALLBACK* get_other_value)(
      struct _cef_api_version_test_scoped_client_child_v2_t* self);

#if CEF_API_ADDED(13303)
  ///
  /// Return another value.
  ///
  int(CEF_CALLBACK* get_another_value)(
      struct _cef_api_version_test_scoped_client_child_v2_t* self);
#endif
} cef_api_version_test_scoped_client_child_v2_t;

#endif  // CEF_API_ADDED(13302)

#ifdef __cplusplus
}
#endif

#endif  // CEF_INCLUDE_CAPI_TEST_CEF_API_VERSION_TEST_CAPI_H_
