// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=1c326bb01f0f44b6896129dbe84eb55af68b8533$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_TEST_TRANSLATOR_TEST_SCOPED_CLIENT_CHILD_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_TEST_TRANSLATOR_TEST_SCOPED_CLIENT_CHILD_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/test/cef_translator_test_capi.h"
#include "include/test/cef_translator_test.h"
#include "libcef_dll/cpptoc/cpptoc_scoped.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefTranslatorTestScopedClientChildCppToC
    : public CefCppToCScoped<CefTranslatorTestScopedClientChildCppToC,
                             CefTranslatorTestScopedClientChild,
                             cef_translator_test_scoped_client_child_t> {
 public:
  CefTranslatorTestScopedClientChildCppToC();
  virtual ~CefTranslatorTestScopedClientChildCppToC();
};

constexpr auto CefTranslatorTestScopedClientChildCppToC_WrapOwn =
    CefTranslatorTestScopedClientChildCppToC::WrapOwn;
constexpr auto CefTranslatorTestScopedClientChildCppToC_WrapRaw =
    CefTranslatorTestScopedClientChildCppToC::WrapRaw;
constexpr auto CefTranslatorTestScopedClientChildCppToC_UnwrapOwn =
    CefTranslatorTestScopedClientChildCppToC::UnwrapOwn;
constexpr auto CefTranslatorTestScopedClientChildCppToC_UnwrapRaw =
    CefTranslatorTestScopedClientChildCppToC::UnwrapRaw;
constexpr auto CefTranslatorTestScopedClientChildCppToC_GetWrapper =
    CefTranslatorTestScopedClientChildCppToC::GetWrapper;

inline cef_translator_test_scoped_client_child_t*
CefTranslatorTestScopedClientChildCppToC_WrapRawAndRelease(
    CefRawPtr<CefTranslatorTestScopedClientChild> c) {
  auto [ownerPtr, structPtr] =
      CefTranslatorTestScopedClientChildCppToC_WrapRaw(c);
  ownerPtr.release();
  return structPtr;
}

#endif  // CEF_LIBCEF_DLL_CPPTOC_TEST_TRANSLATOR_TEST_SCOPED_CLIENT_CHILD_CPPTOC_H_
