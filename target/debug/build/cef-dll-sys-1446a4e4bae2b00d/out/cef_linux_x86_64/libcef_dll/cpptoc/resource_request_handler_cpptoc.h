// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=bb14703c2470d5bc3e17097527a2f7c90c789ded$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_RESOURCE_REQUEST_HANDLER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_RESOURCE_REQUEST_HANDLER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_resource_request_handler_capi.h"
#include "include/cef_resource_request_handler.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefResourceRequestHandlerCppToC
    : public CefCppToCRefCounted<CefResourceRequestHandlerCppToC,
                                 CefResourceRequestHandler,
                                 cef_resource_request_handler_t> {
 public:
  CefResourceRequestHandlerCppToC();
  virtual ~CefResourceRequestHandlerCppToC();
};

constexpr auto CefResourceRequestHandlerCppToC_Wrap =
    CefResourceRequestHandlerCppToC::Wrap;
constexpr auto CefResourceRequestHandlerCppToC_Unwrap =
    CefResourceRequestHandlerCppToC::Unwrap;

#endif  // CEF_LIBCEF_DLL_CPPTOC_RESOURCE_REQUEST_HANDLER_CPPTOC_H_
