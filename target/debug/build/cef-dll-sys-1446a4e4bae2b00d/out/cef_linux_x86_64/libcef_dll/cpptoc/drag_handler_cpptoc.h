// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=c747370d0c6c2e019ed7c5e86fd5f8a50640103c$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_DRAG_HANDLER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_DRAG_HANDLER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_drag_handler_capi.h"
#include "include/cef_drag_handler.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefDragHandlerCppToC : public CefCppToCRefCounted<CefDragHandlerCppToC,
                                                        CefDragHandler,
                                                        cef_drag_handler_t> {
 public:
  CefDragHandlerCppToC();
  virtual ~CefDragHandlerCppToC();
};

constexpr auto CefDragHandlerCppToC_Wrap = CefDragHandlerCppToC::Wrap;
constexpr auto CefDragHandlerCppToC_Unwrap = CefDragHandlerCppToC::Unwrap;

#endif  // CEF_LIBCEF_DLL_CPPTOC_DRAG_HANDLER_CPPTOC_H_
