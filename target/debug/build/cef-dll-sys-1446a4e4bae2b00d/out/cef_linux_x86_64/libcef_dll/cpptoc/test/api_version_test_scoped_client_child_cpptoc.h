// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=93bd0c4ebf01181cf8b2c2179b238861ed58ef01$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_SCOPED_CLIENT_CHILD_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_SCOPED_CLIENT_CHILD_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/test/cef_api_version_test_capi.h"
#include "include/test/cef_api_version_test.h"
#include "libcef_dll/cpptoc/cpptoc_scoped.h"

#if CEF_API_REMOVED(13302)

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefApiVersionTestScopedClientChildCppToC
    : public CefCppToCScoped<CefApiVersionTestScopedClientChildCppToC,
                             CefApiVersionTestScopedClientChild,
                             cef_api_version_test_scoped_client_child_t> {
 public:
  CefApiVersionTestScopedClientChildCppToC();
  virtual ~CefApiVersionTestScopedClientChildCppToC();
};

constexpr auto CefApiVersionTestScopedClientChildCppToC_WrapOwn =
    CefApiVersionTestScopedClientChildCppToC::WrapOwn;
constexpr auto CefApiVersionTestScopedClientChildCppToC_WrapRaw =
    CefApiVersionTestScopedClientChildCppToC::WrapRaw;
constexpr auto CefApiVersionTestScopedClientChildCppToC_UnwrapOwn =
    CefApiVersionTestScopedClientChildCppToC::UnwrapOwn;
constexpr auto CefApiVersionTestScopedClientChildCppToC_UnwrapRaw =
    CefApiVersionTestScopedClientChildCppToC::UnwrapRaw;
constexpr auto CefApiVersionTestScopedClientChildCppToC_GetWrapper =
    CefApiVersionTestScopedClientChildCppToC::GetWrapper;

inline cef_api_version_test_scoped_client_child_t*
CefApiVersionTestScopedClientChildCppToC_WrapRawAndRelease(
    CefRawPtr<CefApiVersionTestScopedClientChild> c) {
  auto [ownerPtr, structPtr] =
      CefApiVersionTestScopedClientChildCppToC_WrapRaw(c);
  ownerPtr.release();
  return structPtr;
}

#endif  // CEF_API_REMOVED(13302)

#endif  // CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_SCOPED_CLIENT_CHILD_CPPTOC_H_
