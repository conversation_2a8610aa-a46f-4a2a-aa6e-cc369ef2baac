// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=c10da3a720e124d12a589761da942ce50b17b57c$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_SETTING_OBSERVER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_SETTING_OBSERVER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_request_context_capi.h"
#include "include/capi/cef_request_context_handler_capi.h"
#include "include/capi/cef_scheme_capi.h"
#include "include/cef_request_context.h"
#include "include/cef_request_context_handler.h"
#include "include/cef_scheme.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

#if CEF_API_ADDED(13401)

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefSettingObserverCppToC
    : public CefCppToCRefCounted<CefSettingObserverCppToC,
                                 CefSettingObserver,
                                 cef_setting_observer_t> {
 public:
  CefSettingObserverCppToC();
  virtual ~CefSettingObserverCppToC();
};

constexpr auto CefSettingObserverCppToC_Wrap = CefSettingObserverCppToC::Wrap;
constexpr auto CefSettingObserverCppToC_Unwrap =
    CefSettingObserverCppToC::Unwrap;

#endif  // CEF_API_ADDED(13401)

#endif  // CEF_LIBCEF_DLL_CPPTOC_SETTING_OBSERVER_CPPTOC_H_
