// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=e6d19214e59c15a2508e002e59a47c886cb4eb55$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_RESPONSE_FILTER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_RESPONSE_FILTER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_response_filter_capi.h"
#include "include/cef_response_filter.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefResponseFilterCppToC
    : public CefCppToCRefCounted<CefResponseFilterCppToC,
                                 CefResponseFilter,
                                 cef_response_filter_t> {
 public:
  CefResponseFilterCppToC();
  virtual ~CefResponseFilterCppToC();
};

constexpr auto CefResponseFilterCppToC_Wrap = CefResponseFilterCppToC::Wrap;
constexpr auto CefResponseFilterCppToC_Unwrap = CefResponseFilterCppToC::Unwrap;

#endif  // CEF_LIBCEF_DLL_CPPTOC_RESPONSE_FILTER_CPPTOC_H_
