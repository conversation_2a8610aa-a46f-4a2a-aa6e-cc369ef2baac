// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=a37b61750371ff65f6ae71cac2aa156af3d89b47$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_TEST_TEST_SERVER_HANDLER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_TEST_TEST_SERVER_HANDLER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/test/cef_test_server_capi.h"
#include "include/test/cef_test_server.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefTestServerHandlerCppToC
    : public CefCppToCRefCounted<CefTestServerHandlerCppToC,
                                 CefTestServerHandler,
                                 cef_test_server_handler_t> {
 public:
  CefTestServerHandlerCppToC();
  virtual ~CefTestServerHandlerCppToC();
};

constexpr auto CefTestServerHandlerCppToC_Wrap =
    CefTestServerHandlerCppToC::Wrap;
constexpr auto CefTestServerHandlerCppToC_Unwrap =
    CefTestServerHandlerCppToC::Unwrap;

#endif  // CEF_LIBCEF_DLL_CPPTOC_TEST_TEST_SERVER_HANDLER_CPPTOC_H_
