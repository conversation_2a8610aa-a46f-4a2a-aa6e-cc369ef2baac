// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=c8d4ccac06fd5d8ce9316cb7c5199e6254db369e$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_REF_PTR_CLIENT_CHILD_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_REF_PTR_CLIENT_CHILD_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/test/cef_api_version_test_capi.h"
#include "include/test/cef_api_version_test.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

#if CEF_API_REMOVED(13302)

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefApiVersionTestRefPtrClientChildCppToC
    : public CefCppToCRefCounted<CefApiVersionTestRefPtrClientChildCppToC,
                                 CefApiVersionTestRefPtrClientChild,
                                 cef_api_version_test_ref_ptr_client_child_t> {
 public:
  CefApiVersionTestRefPtrClientChildCppToC();
  virtual ~CefApiVersionTestRefPtrClientChildCppToC();
};

constexpr auto CefApiVersionTestRefPtrClientChildCppToC_Wrap =
    CefApiVersionTestRefPtrClientChildCppToC::Wrap;
constexpr auto CefApiVersionTestRefPtrClientChildCppToC_Unwrap =
    CefApiVersionTestRefPtrClientChildCppToC::Unwrap;

#endif  // CEF_API_REMOVED(13302)

#endif  // CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_REF_PTR_CLIENT_CHILD_CPPTOC_H_
