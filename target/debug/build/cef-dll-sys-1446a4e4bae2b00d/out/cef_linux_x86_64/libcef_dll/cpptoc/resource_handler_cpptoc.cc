// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=5e2fd05b8ca225b2a54a72087a278de139ad6d7f$
//

#include "libcef_dll/cpptoc/resource_handler_cpptoc.h"

#include "libcef_dll/ctocpp/callback_ctocpp.h"
#include "libcef_dll/ctocpp/request_ctocpp.h"
#include "libcef_dll/ctocpp/resource_read_callback_ctocpp.h"
#include "libcef_dll/ctocpp/resource_skip_callback_ctocpp.h"
#include "libcef_dll/ctocpp/response_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

namespace {

// MEMBER FUNCTIONS - Body may be edited by hand.

int CEF_CALLBACK resource_handler_open(struct _cef_resource_handler_t* self,
                                       struct _cef_request_t* request,
                                       int* handle_request,
                                       struct _cef_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: request; type: refptr_diff
  DCHECK(request);
  if (!request) {
    return 0;
  }
  // Verify param: handle_request; type: bool_byref
  DCHECK(handle_request);
  if (!handle_request) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Translate param: handle_request; type: bool_byref
  bool handle_requestBool = (handle_request && *handle_request) ? true : false;

  // Execute
  bool _retval = CefResourceHandlerCppToC::Get(self)->Open(
      CefRequestCToCpp_Wrap(request), handle_requestBool,
      CefCallbackCToCpp_Wrap(callback));

  // Restore param: handle_request; type: bool_byref
  if (handle_request) {
    *handle_request = handle_requestBool ? true : false;
  }

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK
resource_handler_process_request(struct _cef_resource_handler_t* self,
                                 struct _cef_request_t* request,
                                 struct _cef_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: request; type: refptr_diff
  DCHECK(request);
  if (!request) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Execute
  bool _retval = CefResourceHandlerCppToC::Get(self)->ProcessRequest(
      CefRequestCToCpp_Wrap(request), CefCallbackCToCpp_Wrap(callback));

  // Return type: bool
  return _retval;
}

void CEF_CALLBACK
resource_handler_get_response_headers(struct _cef_resource_handler_t* self,
                                      struct _cef_response_t* response,
                                      int64_t* response_length,
                                      cef_string_t* redirectUrl) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: response; type: refptr_diff
  DCHECK(response);
  if (!response) {
    return;
  }
  // Verify param: response_length; type: simple_byref
  DCHECK(response_length);
  if (!response_length) {
    return;
  }
  // Verify param: redirectUrl; type: string_byref
  DCHECK(redirectUrl);
  if (!redirectUrl) {
    return;
  }

  // Translate param: response_length; type: simple_byref
  int64_t response_lengthVal = response_length ? *response_length : 0;
  // Translate param: redirectUrl; type: string_byref
  CefString redirectUrlStr(redirectUrl);

  // Execute
  CefResourceHandlerCppToC::Get(self)->GetResponseHeaders(
      CefResponseCToCpp_Wrap(response), response_lengthVal, redirectUrlStr);

  // Restore param: response_length; type: simple_byref
  if (response_length) {
    *response_length = response_lengthVal;
  }
}

int CEF_CALLBACK
resource_handler_skip(struct _cef_resource_handler_t* self,
                      int64_t bytes_to_skip,
                      int64_t* bytes_skipped,
                      struct _cef_resource_skip_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: bytes_skipped; type: simple_byref
  DCHECK(bytes_skipped);
  if (!bytes_skipped) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Translate param: bytes_skipped; type: simple_byref
  int64_t bytes_skippedVal = bytes_skipped ? *bytes_skipped : 0;

  // Execute
  bool _retval = CefResourceHandlerCppToC::Get(self)->Skip(
      bytes_to_skip, bytes_skippedVal,
      CefResourceSkipCallbackCToCpp_Wrap(callback));

  // Restore param: bytes_skipped; type: simple_byref
  if (bytes_skipped) {
    *bytes_skipped = bytes_skippedVal;
  }

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK
resource_handler_read(struct _cef_resource_handler_t* self,
                      void* data_out,
                      int bytes_to_read,
                      int* bytes_read,
                      struct _cef_resource_read_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: data_out; type: simple_byaddr
  DCHECK(data_out);
  if (!data_out) {
    return 0;
  }
  // Verify param: bytes_read; type: simple_byref
  DCHECK(bytes_read);
  if (!bytes_read) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Translate param: bytes_read; type: simple_byref
  int bytes_readVal = bytes_read ? *bytes_read : 0;

  // Execute
  bool _retval = CefResourceHandlerCppToC::Get(self)->Read(
      data_out, bytes_to_read, bytes_readVal,
      CefResourceReadCallbackCToCpp_Wrap(callback));

  // Restore param: bytes_read; type: simple_byref
  if (bytes_read) {
    *bytes_read = bytes_readVal;
  }

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK
resource_handler_read_response(struct _cef_resource_handler_t* self,
                               void* data_out,
                               int bytes_to_read,
                               int* bytes_read,
                               struct _cef_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: data_out; type: simple_byaddr
  DCHECK(data_out);
  if (!data_out) {
    return 0;
  }
  // Verify param: bytes_read; type: simple_byref
  DCHECK(bytes_read);
  if (!bytes_read) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Translate param: bytes_read; type: simple_byref
  int bytes_readVal = bytes_read ? *bytes_read : 0;

  // Execute
  bool _retval = CefResourceHandlerCppToC::Get(self)->ReadResponse(
      data_out, bytes_to_read, bytes_readVal, CefCallbackCToCpp_Wrap(callback));

  // Restore param: bytes_read; type: simple_byref
  if (bytes_read) {
    *bytes_read = bytes_readVal;
  }

  // Return type: bool
  return _retval;
}

void CEF_CALLBACK
resource_handler_cancel(struct _cef_resource_handler_t* self) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }

  // Execute
  CefResourceHandlerCppToC::Get(self)->Cancel();
}

}  // namespace

// CONSTRUCTOR - Do not edit by hand.

CefResourceHandlerCppToC::CefResourceHandlerCppToC() {
  GetStruct()->open = resource_handler_open;
  GetStruct()->process_request = resource_handler_process_request;
  GetStruct()->get_response_headers = resource_handler_get_response_headers;
  GetStruct()->skip = resource_handler_skip;
  GetStruct()->read = resource_handler_read;
  GetStruct()->read_response = resource_handler_read_response;
  GetStruct()->cancel = resource_handler_cancel;
}

// DESTRUCTOR - Do not edit by hand.

CefResourceHandlerCppToC::~CefResourceHandlerCppToC() {
  shutdown_checker::AssertNotShutdown();
}

template <>
CefRefPtr<CefResourceHandler> CefCppToCRefCounted<
    CefResourceHandlerCppToC,
    CefResourceHandler,
    cef_resource_handler_t>::UnwrapDerived(CefWrapperType type,
                                           cef_resource_handler_t* s) {
  CHECK(false) << __func__ << " called with unexpected class type " << type;
  return nullptr;
}

template <>
CefWrapperType CefCppToCRefCounted<CefResourceHandlerCppToC,
                                   CefResourceHandler,
                                   cef_resource_handler_t>::kWrapperType =
    WT_RESOURCE_HANDLER;
