// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=d8746f2084f0bfa2a1d2afddcf60a424a3ea0e19$
//

#include "libcef_dll/cpptoc/request_handler_cpptoc.h"

#include "libcef_dll/cpptoc/resource_request_handler_cpptoc.h"
#include "libcef_dll/ctocpp/auth_callback_ctocpp.h"
#include "libcef_dll/ctocpp/browser_ctocpp.h"
#include "libcef_dll/ctocpp/callback_ctocpp.h"
#include "libcef_dll/ctocpp/frame_ctocpp.h"
#include "libcef_dll/ctocpp/request_ctocpp.h"
#include "libcef_dll/ctocpp/select_client_certificate_callback_ctocpp.h"
#include "libcef_dll/ctocpp/sslinfo_ctocpp.h"
#include "libcef_dll/ctocpp/unresponsive_process_callback_ctocpp.h"
#include "libcef_dll/ctocpp/x509_certificate_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

namespace {

// MEMBER FUNCTIONS - Body may be edited by hand.

int CEF_CALLBACK
request_handler_on_before_browse(struct _cef_request_handler_t* self,
                                 struct _cef_browser_t* browser,
                                 struct _cef_frame_t* frame,
                                 struct _cef_request_t* request,
                                 int user_gesture,
                                 int is_redirect) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }
  // Verify param: frame; type: refptr_diff
  DCHECK(frame);
  if (!frame) {
    return 0;
  }
  // Verify param: request; type: refptr_diff
  DCHECK(request);
  if (!request) {
    return 0;
  }

  // Execute
  bool _retval = CefRequestHandlerCppToC::Get(self)->OnBeforeBrowse(
      CefBrowserCToCpp_Wrap(browser), CefFrameCToCpp_Wrap(frame),
      CefRequestCToCpp_Wrap(request), user_gesture ? true : false,
      is_redirect ? true : false);

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK request_handler_on_open_urlfrom_tab(
    struct _cef_request_handler_t* self,
    struct _cef_browser_t* browser,
    struct _cef_frame_t* frame,
    const cef_string_t* target_url,
    cef_window_open_disposition_t target_disposition,
    int user_gesture) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }
  // Verify param: frame; type: refptr_diff
  DCHECK(frame);
  if (!frame) {
    return 0;
  }
  // Verify param: target_url; type: string_byref_const
  DCHECK(target_url);
  if (!target_url) {
    return 0;
  }

  // Execute
  bool _retval = CefRequestHandlerCppToC::Get(self)->OnOpenURLFromTab(
      CefBrowserCToCpp_Wrap(browser), CefFrameCToCpp_Wrap(frame),
      CefString(target_url), target_disposition, user_gesture ? true : false);

  // Return type: bool
  return _retval;
}

struct _cef_resource_request_handler_t* CEF_CALLBACK
request_handler_get_resource_request_handler(
    struct _cef_request_handler_t* self,
    struct _cef_browser_t* browser,
    struct _cef_frame_t* frame,
    struct _cef_request_t* request,
    int is_navigation,
    int is_download,
    const cef_string_t* request_initiator,
    int* disable_default_handling) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return NULL;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return NULL;
  }
  // Verify param: frame; type: refptr_diff
  DCHECK(frame);
  if (!frame) {
    return NULL;
  }
  // Verify param: request; type: refptr_diff
  DCHECK(request);
  if (!request) {
    return NULL;
  }
  // Verify param: disable_default_handling; type: bool_byref
  DCHECK(disable_default_handling);
  if (!disable_default_handling) {
    return NULL;
  }
  // Unverified params: request_initiator

  // Translate param: disable_default_handling; type: bool_byref
  bool disable_default_handlingBool =
      (disable_default_handling && *disable_default_handling) ? true : false;

  // Execute
  CefRefPtr<CefResourceRequestHandler> _retval =
      CefRequestHandlerCppToC::Get(self)->GetResourceRequestHandler(
          CefBrowserCToCpp_Wrap(browser), CefFrameCToCpp_Wrap(frame),
          CefRequestCToCpp_Wrap(request), is_navigation ? true : false,
          is_download ? true : false, CefString(request_initiator),
          disable_default_handlingBool);

  // Restore param: disable_default_handling; type: bool_byref
  if (disable_default_handling) {
    *disable_default_handling = disable_default_handlingBool ? true : false;
  }

  // Return type: refptr_same
  return CefResourceRequestHandlerCppToC_Wrap(_retval);
}

int CEF_CALLBACK
request_handler_get_auth_credentials(struct _cef_request_handler_t* self,
                                     struct _cef_browser_t* browser,
                                     const cef_string_t* origin_url,
                                     int isProxy,
                                     const cef_string_t* host,
                                     int port,
                                     const cef_string_t* realm,
                                     const cef_string_t* scheme,
                                     struct _cef_auth_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }
  // Verify param: origin_url; type: string_byref_const
  DCHECK(origin_url);
  if (!origin_url) {
    return 0;
  }
  // Verify param: host; type: string_byref_const
  DCHECK(host);
  if (!host) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }
  // Unverified params: realm, scheme

  // Execute
  bool _retval = CefRequestHandlerCppToC::Get(self)->GetAuthCredentials(
      CefBrowserCToCpp_Wrap(browser), CefString(origin_url),
      isProxy ? true : false, CefString(host), port, CefString(realm),
      CefString(scheme), CefAuthCallbackCToCpp_Wrap(callback));

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK
request_handler_on_certificate_error(struct _cef_request_handler_t* self,
                                     struct _cef_browser_t* browser,
                                     cef_errorcode_t cert_error,
                                     const cef_string_t* request_url,
                                     struct _cef_sslinfo_t* ssl_info,
                                     struct _cef_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }
  // Verify param: request_url; type: string_byref_const
  DCHECK(request_url);
  if (!request_url) {
    return 0;
  }
  // Verify param: ssl_info; type: refptr_diff
  DCHECK(ssl_info);
  if (!ssl_info) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Execute
  bool _retval = CefRequestHandlerCppToC::Get(self)->OnCertificateError(
      CefBrowserCToCpp_Wrap(browser), cert_error, CefString(request_url),
      CefSSLInfoCToCpp_Wrap(ssl_info), CefCallbackCToCpp_Wrap(callback));

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK request_handler_on_select_client_certificate(
    struct _cef_request_handler_t* self,
    struct _cef_browser_t* browser,
    int isProxy,
    const cef_string_t* host,
    int port,
    size_t certificatesCount,
    struct _cef_x509_certificate_t* const* certificates,
    struct _cef_select_client_certificate_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }
  // Verify param: host; type: string_byref_const
  DCHECK(host);
  if (!host) {
    return 0;
  }
  // Verify param: certificates; type: refptr_vec_diff_byref_const
  DCHECK(certificatesCount == 0 || certificates);
  if (certificatesCount > 0 && !certificates) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Translate param: certificates; type: refptr_vec_diff_byref_const
  std::vector<CefRefPtr<CefX509Certificate>> certificatesList;
  if (certificatesCount > 0) {
    for (size_t i = 0; i < certificatesCount; ++i) {
      CefRefPtr<CefX509Certificate> certificatesVal =
          CefX509CertificateCToCpp_Wrap(certificates[i]);
      certificatesList.push_back(certificatesVal);
    }
  }

  // Execute
  bool _retval = CefRequestHandlerCppToC::Get(self)->OnSelectClientCertificate(
      CefBrowserCToCpp_Wrap(browser), isProxy ? true : false, CefString(host),
      port, certificatesList,
      CefSelectClientCertificateCallbackCToCpp_Wrap(callback));

  // Return type: bool
  return _retval;
}

void CEF_CALLBACK
request_handler_on_render_view_ready(struct _cef_request_handler_t* self,
                                     struct _cef_browser_t* browser) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return;
  }

  // Execute
  CefRequestHandlerCppToC::Get(self)->OnRenderViewReady(
      CefBrowserCToCpp_Wrap(browser));
}

int CEF_CALLBACK request_handler_on_render_process_unresponsive(
    struct _cef_request_handler_t* self,
    struct _cef_browser_t* browser,
    struct _cef_unresponsive_process_callback_t* callback) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback);
  if (!callback) {
    return 0;
  }

  // Execute
  bool _retval =
      CefRequestHandlerCppToC::Get(self)->OnRenderProcessUnresponsive(
          CefBrowserCToCpp_Wrap(browser),
          CefUnresponsiveProcessCallbackCToCpp_Wrap(callback));

  // Return type: bool
  return _retval;
}

void CEF_CALLBACK request_handler_on_render_process_responsive(
    struct _cef_request_handler_t* self,
    struct _cef_browser_t* browser) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return;
  }

  // Execute
  CefRequestHandlerCppToC::Get(self)->OnRenderProcessResponsive(
      CefBrowserCToCpp_Wrap(browser));
}

void CEF_CALLBACK request_handler_on_render_process_terminated(
    struct _cef_request_handler_t* self,
    struct _cef_browser_t* browser,
    cef_termination_status_t status,
    int error_code,
    const cef_string_t* error_string) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return;
  }
  // Verify param: error_string; type: string_byref_const
  DCHECK(error_string);
  if (!error_string) {
    return;
  }

  // Execute
  CefRequestHandlerCppToC::Get(self)->OnRenderProcessTerminated(
      CefBrowserCToCpp_Wrap(browser), status, error_code,
      CefString(error_string));
}

void CEF_CALLBACK request_handler_on_document_available_in_main_frame(
    struct _cef_request_handler_t* self,
    struct _cef_browser_t* browser) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return;
  }

  // Execute
  CefRequestHandlerCppToC::Get(self)->OnDocumentAvailableInMainFrame(
      CefBrowserCToCpp_Wrap(browser));
}

}  // namespace

// CONSTRUCTOR - Do not edit by hand.

CefRequestHandlerCppToC::CefRequestHandlerCppToC() {
  GetStruct()->on_before_browse = request_handler_on_before_browse;
  GetStruct()->on_open_urlfrom_tab = request_handler_on_open_urlfrom_tab;
  GetStruct()->get_resource_request_handler =
      request_handler_get_resource_request_handler;
  GetStruct()->get_auth_credentials = request_handler_get_auth_credentials;
  GetStruct()->on_certificate_error = request_handler_on_certificate_error;
  GetStruct()->on_select_client_certificate =
      request_handler_on_select_client_certificate;
  GetStruct()->on_render_view_ready = request_handler_on_render_view_ready;
  GetStruct()->on_render_process_unresponsive =
      request_handler_on_render_process_unresponsive;
  GetStruct()->on_render_process_responsive =
      request_handler_on_render_process_responsive;
  GetStruct()->on_render_process_terminated =
      request_handler_on_render_process_terminated;
  GetStruct()->on_document_available_in_main_frame =
      request_handler_on_document_available_in_main_frame;
}

// DESTRUCTOR - Do not edit by hand.

CefRequestHandlerCppToC::~CefRequestHandlerCppToC() {
  shutdown_checker::AssertNotShutdown();
}

template <>
CefRefPtr<CefRequestHandler> CefCppToCRefCounted<
    CefRequestHandlerCppToC,
    CefRequestHandler,
    cef_request_handler_t>::UnwrapDerived(CefWrapperType type,
                                          cef_request_handler_t* s) {
  CHECK(false) << __func__ << " called with unexpected class type " << type;
  return nullptr;
}

template <>
CefWrapperType CefCppToCRefCounted<CefRequestHandlerCppToC,
                                   CefRequestHandler,
                                   cef_request_handler_t>::kWrapperType =
    WT_REQUEST_HANDLER;
