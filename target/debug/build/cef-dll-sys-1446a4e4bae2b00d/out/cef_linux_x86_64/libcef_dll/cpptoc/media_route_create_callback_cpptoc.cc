// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=2aff2d51154dddea48b2e6649b66b55856177936$
//

#include "libcef_dll/cpptoc/media_route_create_callback_cpptoc.h"

#include "libcef_dll/ctocpp/media_route_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

namespace {

// MEMBER FUNCTIONS - Body may be edited by hand.

void CEF_CALLBACK media_route_create_callback_on_media_route_create_finished(
    struct _cef_media_route_create_callback_t* self,
    cef_media_route_create_result_t result,
    const cef_string_t* error,
    struct _cef_media_route_t* route) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Unverified params: error, route

  // Execute
  CefMediaRouteCreateCallbackCppToC::Get(self)->OnMediaRouteCreateFinished(
      result, CefString(error), CefMediaRouteCToCpp_Wrap(route));
}

}  // namespace

// CONSTRUCTOR - Do not edit by hand.

CefMediaRouteCreateCallbackCppToC::CefMediaRouteCreateCallbackCppToC() {
  GetStruct()->on_media_route_create_finished =
      media_route_create_callback_on_media_route_create_finished;
}

// DESTRUCTOR - Do not edit by hand.

CefMediaRouteCreateCallbackCppToC::~CefMediaRouteCreateCallbackCppToC() {
  shutdown_checker::AssertNotShutdown();
}

template <>
CefRefPtr<CefMediaRouteCreateCallback>
CefCppToCRefCounted<CefMediaRouteCreateCallbackCppToC,
                    CefMediaRouteCreateCallback,
                    cef_media_route_create_callback_t>::
    UnwrapDerived(CefWrapperType type, cef_media_route_create_callback_t* s) {
  CHECK(false) << __func__ << " called with unexpected class type " << type;
  return nullptr;
}

template <>
CefWrapperType
    CefCppToCRefCounted<CefMediaRouteCreateCallbackCppToC,
                        CefMediaRouteCreateCallback,
                        cef_media_route_create_callback_t>::kWrapperType =
        WT_MEDIA_ROUTE_CREATE_CALLBACK;
