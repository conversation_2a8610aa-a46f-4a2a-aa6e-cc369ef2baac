// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=0c325578ccffbf4d269a318896b3940dcddeed1c$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_SCOPED_CLIENT_CHILD_V2_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_SCOPED_CLIENT_CHILD_V2_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/test/cef_api_version_test_capi.h"
#include "include/test/cef_api_version_test.h"
#include "libcef_dll/cpptoc/cpptoc_scoped.h"

#if CEF_API_ADDED(13302)

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefApiVersionTestScopedClientChildV2CppToC
    : public CefCppToCScoped<CefApiVersionTestScopedClientChildV2CppToC,
                             CefApiVersionTestScopedClientChildV2,
                             cef_api_version_test_scoped_client_child_v2_t> {
 public:
  CefApiVersionTestScopedClientChildV2CppToC();
  virtual ~CefApiVersionTestScopedClientChildV2CppToC();
};

constexpr auto CefApiVersionTestScopedClientChildV2CppToC_WrapOwn =
    CefApiVersionTestScopedClientChildV2CppToC::WrapOwn;
constexpr auto CefApiVersionTestScopedClientChildV2CppToC_WrapRaw =
    CefApiVersionTestScopedClientChildV2CppToC::WrapRaw;
constexpr auto CefApiVersionTestScopedClientChildV2CppToC_UnwrapOwn =
    CefApiVersionTestScopedClientChildV2CppToC::UnwrapOwn;
constexpr auto CefApiVersionTestScopedClientChildV2CppToC_UnwrapRaw =
    CefApiVersionTestScopedClientChildV2CppToC::UnwrapRaw;
constexpr auto CefApiVersionTestScopedClientChildV2CppToC_GetWrapper =
    CefApiVersionTestScopedClientChildV2CppToC::GetWrapper;

inline cef_api_version_test_scoped_client_child_v2_t*
CefApiVersionTestScopedClientChildV2CppToC_WrapRawAndRelease(
    CefRawPtr<CefApiVersionTestScopedClientChildV2> c) {
  auto [ownerPtr, structPtr] =
      CefApiVersionTestScopedClientChildV2CppToC_WrapRaw(c);
  ownerPtr.release();
  return structPtr;
}

#endif  // CEF_API_ADDED(13302)

#endif  // CEF_LIBCEF_DLL_CPPTOC_TEST_API_VERSION_TEST_SCOPED_CLIENT_CHILD_V2_CPPTOC_H_
