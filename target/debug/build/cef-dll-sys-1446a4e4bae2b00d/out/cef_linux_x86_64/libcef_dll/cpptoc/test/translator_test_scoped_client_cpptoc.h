// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=5e157bd86ba33852c38104f2a33f999ca93ef287$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_TEST_TRANSLATOR_TEST_SCOPED_CLIENT_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_TEST_TRANSLATOR_TEST_SCOPED_CLIENT_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/test/cef_translator_test_capi.h"
#include "include/test/cef_translator_test.h"
#include "libcef_dll/cpptoc/cpptoc_scoped.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefTranslatorTestScopedClientCppToC
    : public CefCppToCScoped<CefTranslatorTestScopedClientCppToC,
                             CefTranslatorTestScopedClient,
                             cef_translator_test_scoped_client_t> {
 public:
  CefTranslatorTestScopedClientCppToC();
  virtual ~CefTranslatorTestScopedClientCppToC();
};

constexpr auto CefTranslatorTestScopedClientCppToC_WrapOwn =
    CefTranslatorTestScopedClientCppToC::WrapOwn;
constexpr auto CefTranslatorTestScopedClientCppToC_WrapRaw =
    CefTranslatorTestScopedClientCppToC::WrapRaw;
constexpr auto CefTranslatorTestScopedClientCppToC_UnwrapOwn =
    CefTranslatorTestScopedClientCppToC::UnwrapOwn;
constexpr auto CefTranslatorTestScopedClientCppToC_UnwrapRaw =
    CefTranslatorTestScopedClientCppToC::UnwrapRaw;
constexpr auto CefTranslatorTestScopedClientCppToC_GetWrapper =
    CefTranslatorTestScopedClientCppToC::GetWrapper;

inline cef_translator_test_scoped_client_t*
CefTranslatorTestScopedClientCppToC_WrapRawAndRelease(
    CefRawPtr<CefTranslatorTestScopedClient> c) {
  auto [ownerPtr, structPtr] = CefTranslatorTestScopedClientCppToC_WrapRaw(c);
  ownerPtr.release();
  return structPtr;
}

#endif  // CEF_LIBCEF_DLL_CPPTOC_TEST_TRANSLATOR_TEST_SCOPED_CLIENT_CPPTOC_H_
