// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=6a795f19f15955a5a8c3ff689544165a583eed3f$
//

#include "libcef_dll/cpptoc/test/api_version_test_scoped_client_cpptoc.h"

#include "libcef_dll/cpptoc/test/api_version_test_scoped_client_child_cpptoc.h"
#include "libcef_dll/cpptoc/test/api_version_test_scoped_client_child_v2_cpptoc.h"

namespace {

// MEMBER FUNCTIONS - Body may be edited by hand.

int CEF_CALLBACK api_version_test_scoped_client_get_value_legacy(
    struct _cef_api_version_test_scoped_client_t* self) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }

  // Execute
  int _retval =
      CefApiVersionTestScopedClientCppToC::Get(self)->GetValueLegacy();

  // Return type: simple
  return _retval;
}

#if CEF_API_ADDED(CEF_EXPERIMENTAL)
int CEF_CALLBACK api_version_test_scoped_client_get_value_exp(
    struct _cef_api_version_test_scoped_client_t* self) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }

  // Execute
  int _retval = CefApiVersionTestScopedClientCppToC::Get(self)->GetValueExp();

  // Return type: simple
  return _retval;
}
#endif  // CEF_API_ADDED(CEF_EXPERIMENTAL)

#if CEF_API_REMOVED(13301)
int CEF_CALLBACK api_version_test_scoped_client_get_value(
    struct _cef_api_version_test_scoped_client_t* self) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }

  // Execute
  int _retval = CefApiVersionTestScopedClientCppToC::Get(self)->GetValue();

  // Return type: simple
  return _retval;
}
#endif  // CEF_API_REMOVED(13301)

#if CEF_API_RANGE(13301, 13302)
int CEF_CALLBACK api_version_test_scoped_client_get_value_v1(
    struct _cef_api_version_test_scoped_client_t* self) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }

  // Execute
  int _retval = CefApiVersionTestScopedClientCppToC::Get(self)->GetValueV1();

  // Return type: simple
  return _retval;
}
#endif  // CEF_API_RANGE(13301, 13302)

#if CEF_API_ADDED(13302)
int CEF_CALLBACK api_version_test_scoped_client_get_value_v2(
    struct _cef_api_version_test_scoped_client_t* self) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }

  // Execute
  int _retval = CefApiVersionTestScopedClientCppToC::Get(self)->GetValueV2();

  // Return type: simple
  return _retval;
}
#endif  // CEF_API_ADDED(13302)

}  // namespace

// CONSTRUCTOR - Do not edit by hand.

CefApiVersionTestScopedClientCppToC::CefApiVersionTestScopedClientCppToC() {
  GetStruct()->get_value_legacy =
      api_version_test_scoped_client_get_value_legacy;
#if CEF_API_REMOVED(13301)
  GetStruct()->get_value = api_version_test_scoped_client_get_value;
#endif
#if CEF_API_RANGE(13301, 13302)
  GetStruct()->get_value_v1 = api_version_test_scoped_client_get_value_v1;
#endif
#if CEF_API_ADDED(13302)
  GetStruct()->get_value_v2 = api_version_test_scoped_client_get_value_v2;
#endif
#if CEF_API_ADDED(CEF_EXPERIMENTAL)
  GetStruct()->get_value_exp = api_version_test_scoped_client_get_value_exp;
#endif
}

// DESTRUCTOR - Do not edit by hand.

CefApiVersionTestScopedClientCppToC::~CefApiVersionTestScopedClientCppToC() {}

template <>
CefOwnPtr<CefApiVersionTestScopedClient>
CefCppToCScoped<CefApiVersionTestScopedClientCppToC,
                CefApiVersionTestScopedClient,
                cef_api_version_test_scoped_client_t>::
    UnwrapDerivedOwn(CefWrapperType type,
                     cef_api_version_test_scoped_client_t* s) {
#if CEF_API_REMOVED(13302)
  if (type == WT_API_VERSION_TEST_SCOPED_CLIENT_CHILD) {
    return CefApiVersionTestScopedClientChildCppToC_UnwrapOwn(
        reinterpret_cast<cef_api_version_test_scoped_client_child_t*>(s));
  }
#endif
#if CEF_API_ADDED(13302)
  if (type == WT_API_VERSION_TEST_SCOPED_CLIENT_CHILD_V2) {
    return CefApiVersionTestScopedClientChildV2CppToC_UnwrapOwn(
        reinterpret_cast<cef_api_version_test_scoped_client_child_v2_t*>(s));
  }
#endif
  CHECK(false) << __func__ << " called with unexpected class type " << type;
  return nullptr;
}

template <>
CefRawPtr<CefApiVersionTestScopedClient>
CefCppToCScoped<CefApiVersionTestScopedClientCppToC,
                CefApiVersionTestScopedClient,
                cef_api_version_test_scoped_client_t>::
    UnwrapDerivedRaw(CefWrapperType type,
                     cef_api_version_test_scoped_client_t* s) {
#if CEF_API_REMOVED(13302)
  if (type == WT_API_VERSION_TEST_SCOPED_CLIENT_CHILD) {
    return CefApiVersionTestScopedClientChildCppToC_UnwrapRaw(
        reinterpret_cast<cef_api_version_test_scoped_client_child_t*>(s));
  }
#endif
#if CEF_API_ADDED(13302)
  if (type == WT_API_VERSION_TEST_SCOPED_CLIENT_CHILD_V2) {
    return CefApiVersionTestScopedClientChildV2CppToC_UnwrapRaw(
        reinterpret_cast<cef_api_version_test_scoped_client_child_v2_t*>(s));
  }
#endif
  CHECK(false) << __func__ << " called with unexpected class type " << type;
  return nullptr;
}

template <>
CefWrapperType
    CefCppToCScoped<CefApiVersionTestScopedClientCppToC,
                    CefApiVersionTestScopedClient,
                    cef_api_version_test_scoped_client_t>::kWrapperType =
        WT_API_VERSION_TEST_SCOPED_CLIENT;
