// Copyright (c) 2025 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=3d4e2bf3cf445bec765872f317c8390139ef9cc7$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_RENDER_PROCESS_HANDLER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_RENDER_PROCESS_HANDLER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_render_process_handler_capi.h"
#include "include/cef_render_process_handler.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefRenderProcessHandlerCppToC
    : public CefCppToCRefCounted<CefRenderProcessHandlerCppToC,
                                 CefRenderProcessHandler,
                                 cef_render_process_handler_t> {
 public:
  CefRenderProcessHandlerCppToC();
  virtual ~CefRenderProcessHandlerCppToC();
};

constexpr auto CefRenderProcessHandlerCppToC_Wrap =
    CefRenderProcessHandlerCppToC::Wrap;
constexpr auto CefRenderProcessHandlerCppToC_Unwrap =
    CefRenderProcessHandlerCppToC::Unwrap;

#endif  // CEF_LIBCEF_DLL_CPPTOC_RENDER_PROCESS_HANDLER_CPPTOC_H_
