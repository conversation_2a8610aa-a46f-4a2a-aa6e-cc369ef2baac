cargo:rerun-if-env-changed=WEBKIT2GTK_4.1_NO_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=SYSROOT
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_PATH_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_PATH
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_LIBDIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64-unknown-linux-gnu
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR_x86_64_unknown_linux_gnu
cargo:rerun-if-env-changed=HOST_PKG_CONFIG_SYSROOT_DIR
cargo:rerun-if-env-changed=PKG_CONFIG_SYSROOT_DIR
cargo:rustc-link-lib=glib-2.0
cargo:rustc-link-lib=webkit2gtk-4.1
cargo:rustc-link-lib=gtk-3
cargo:rustc-link-lib=gdk-3
cargo:rustc-link-lib=z
cargo:rustc-link-lib=pangocairo-1.0
cargo:rustc-link-lib=pango-1.0
cargo:rustc-link-lib=harfbuzz
cargo:rustc-link-lib=atk-1.0
cargo:rustc-link-lib=cairo-gobject
cargo:rustc-link-lib=cairo
cargo:rustc-link-lib=gdk_pixbuf-2.0
cargo:rustc-link-lib=soup-3.0
cargo:rustc-link-lib=gmodule-2.0
cargo:rustc-link-lib=glib-2.0
cargo:rustc-link-lib=gio-2.0
cargo:rustc-link-lib=javascriptcoregtk-4.1
cargo:rustc-link-lib=gobject-2.0
cargo:rustc-link-lib=glib-2.0
cargo:include=/usr/include/webkitgtk-4.1:/usr/include/glib-2.0:/usr/lib/x86_64-linux-gnu/glib-2.0/include:/usr/include:/usr/include/gtk-3.0:/usr/include/pango-1.0:/usr/include/harfbuzz:/usr/include/freetype2:/usr/include/libpng16:/usr/include/libmount:/usr/include/blkid:/usr/include/fribidi:/usr/include/cairo:/usr/include/pixman-1:/usr/include/gdk-pixbuf-2.0:/usr/include/x86_64-linux-gnu:/usr/include/webp:/usr/include/gio-unix-2.0:/usr/include/atk-1.0:/usr/include/at-spi2-atk/2.0:/usr/include/at-spi-2.0:/usr/include/dbus-1.0:/usr/lib/x86_64-linux-gnu/dbus-1.0/include:/usr/include/libsoup-3.0:/usr/include/sysprof-6
cargo:rerun-if-env-changed=SYSTEM_DEPS_BUILD_INTERNAL
cargo:rerun-if-env-changed=SYSTEM_DEPS_LINK
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_LIB
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_LIB_FRAMEWORK
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_SEARCH_NATIVE
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_SEARCH_FRAMEWORK
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_INCLUDE
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_NO_PKG_CONFIG
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_BUILD_INTERNAL
cargo:rerun-if-env-changed=SYSTEM_DEPS_WEBKIT2GTK_4_1_LINK

cargo:rustc-cfg=system_deps_have_webkit2gtk_4_1
