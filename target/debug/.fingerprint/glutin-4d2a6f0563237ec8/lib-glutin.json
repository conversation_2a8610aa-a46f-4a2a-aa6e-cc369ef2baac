{"rustc": 15597765236515928571, "features": "[\"default\", \"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"egl\", \"glutin_egl_sys\", \"glutin_glx_sys\", \"glutin_wgl_sys\", \"glx\", \"libloading\", \"wayland\", \"wayland-sys\", \"wgl\", \"windows-sys\", \"x11\", \"x11-dl\"]", "target": 1600304521922079521, "profile": 15657897354478470176, "path": 9963722515132079289, "deps": [[3722963349756955755, "once_cell", false, 3342068410030437983], [4143744114649553716, "raw_window_handle", false, 18161212172971865125], [5573101603161346839, "x11_dl", false, 14200985222066716705], [7191709312698686449, "glutin_egl_sys", false, 16757174625260973151], [7896293946984509699, "bitflags", false, 14392917110399809528], [10058659651543567831, "build_script_build", false, 8193115822686618684], [13086770490129249622, "wayland_sys", false, 17129135344263774230], [13587469111750606423, "libloading", false, 10090669283737234085], [17388709782194075699, "glutin_glx_sys", false, 1526714092383620666]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/glutin-4d2a6f0563237ec8/dep-lib-glutin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}