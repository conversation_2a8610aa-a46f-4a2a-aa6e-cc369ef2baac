{"rustc": 15597765236515928571, "features": "[\"glib\", \"use_glib\"]", "declared_features": "[\"freetype\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"v1_18\", \"win32-surface\", \"winapi\", \"x11\", \"xcb\", \"xlib\"]", "target": 12604004911878344227, "profile": 2241668132362809309, "path": 13365569039305138988, "deps": [[2924422107542798392, "libc", false, 4448980353687619144], [6885242093860886281, "build_script_build", false, 12702722230660333606], [13626264195287554611, "glib", false, 17381779316452506963]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/cairo-sys-rs-8870f68a900e2f41/dep-lib-cairo_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}