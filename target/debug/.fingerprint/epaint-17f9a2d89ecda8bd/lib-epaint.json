{"rustc": 15597765236515928571, "features": "[\"bytemuck\", \"default_fonts\", \"epaint_default_fonts\", \"log\"]", "declared_features": "[\"bytemuck\", \"cint\", \"color-hex\", \"deadlock_detection\", \"default\", \"default_fonts\", \"document-features\", \"epaint_default_fonts\", \"log\", \"mint\", \"puffin\", \"rayon\", \"serde\", \"unity\"]", "target": 10495837225410426609, "profile": 15187394197944054659, "path": 3821575880534920666, "deps": [[707738805893328333, "emath", false, 3651701759570360514], [966925859616469517, "ahash", false, 5565911935034144600], [4495526598637097934, "parking_lot", false, 1864152051664162325], [5931649091606299019, "nohash_hasher", false, 6780688689419227744], [5986029879202738730, "log", false, 3297341690800266165], [6511429716036861196, "bytemuck", false, 17095717389127101996], [8146687621941743410, "epaint_default_fonts", false, 15258173826808842860], [9447148682944742144, "ab_glyph", false, 15537183479241957534], [16805990319463827332, "ecolor", false, 11949284214329614007]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/epaint-17f9a2d89ecda8bd/dep-lib-epaint", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}