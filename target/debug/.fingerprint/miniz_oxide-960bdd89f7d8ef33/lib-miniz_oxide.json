{"rustc": 15597765236515928571, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 9544178738263693313, "deps": [[4018467389006652250, "simd_adler32", false, 7756003367206910774], [15407850927583745935, "adler2", false, 17016210086340782500]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/miniz_oxide-960bdd89f7d8ef33/dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}