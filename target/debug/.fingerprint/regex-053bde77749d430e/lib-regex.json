{"rustc": 15597765236515928571, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 15657897354478470176, "path": 15872331581261802795, "deps": [[555019317135488525, "regex_automata", false, 16165918720878660176], [2779309023524819297, "aho_corasick", false, 1947860220590298119], [3129130049864710036, "memchr", false, 2856244634021366478], [9408802513701742484, "regex_syntax", false, 9712658400721876145]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-053bde77749d430e/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}