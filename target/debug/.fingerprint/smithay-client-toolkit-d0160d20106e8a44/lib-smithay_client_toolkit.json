{"rustc": 15597765236515928571, "features": "[\"calloop\", \"calloop-wayland-source\"]", "declared_features": "[\"bytemuck\", \"calloop\", \"calloop-wayland-source\", \"default\", \"pkg-config\", \"xkbcommon\"]", "target": 14828732516350787799, "profile": 15657897354478470176, "path": 4675498943176051154, "deps": [[1089140534224002486, "wayland_cursor", false, 10440412057628720782], [2924422107542798392, "libc", false, 4647260894900377729], [3430646239657634944, "rustix", false, 2473022914811231738], [5130283301485625812, "cursor_icon", false, 16862998141923878333], [5986029879202738730, "log", false, 3297341690800266165], [6279680260381740794, "wayland_csd_frame", false, 6235297999079327662], [7043467959706478429, "build_script_build", false, 1271879791385694198], [7896293946984509699, "bitflags", false, 14392917110399809528], [8008191657135824715, "thiserror", false, 2540229344489490519], [8155749450270499194, "wayland_protocols_wlr", false, 9907650819035377750], [10967003402098758309, "calloop", false, 9067961954520700416], [11718880776180636413, "memmap2", false, 11816167708113259365], [12148808172295771709, "calloop_wayland_source", false, 13683275846251112976], [13177806083327594313, "xkeysym", false, 13868259080645008723], [14820987988873142214, "wayland_protocols", false, 4785928541821906293], [17553607095298811701, "wayland_client", false, 5691400987117699673], [17916187082124099642, "wayland_backend", false, 1478301405608795034], [17920034471148732551, "wayland_scanner", false, 1304274256025628772]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/smithay-client-toolkit-d0160d20106e8a44/dep-lib-smithay_client_toolkit", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}