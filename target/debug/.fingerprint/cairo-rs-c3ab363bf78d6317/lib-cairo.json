{"rustc": 15597765236515928571, "features": "[\"default\", \"glib\", \"use_glib\"]", "declared_features": "[\"default\", \"freetype\", \"freetype-rs\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"v1_18\", \"win32-surface\", \"xcb\", \"xlib\"]", "target": 8694848923278475479, "profile": 15657897354478470176, "path": 8386176290186435836, "deps": [[2924422107542798392, "libc", false, 4647260894900377729], [3722963349756955755, "once_cell", false, 3342068410030437983], [6885242093860886281, "ffi", false, 9745827601473569391], [7896293946984509699, "bitflags", false, 14392917110399809528], [7963079641721436784, "glib", false, 14280722791807577737], [8008191657135824715, "thiserror", false, 2540229344489490519]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/cairo-rs-c3ab363bf78d6317/dep-lib-cairo", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}