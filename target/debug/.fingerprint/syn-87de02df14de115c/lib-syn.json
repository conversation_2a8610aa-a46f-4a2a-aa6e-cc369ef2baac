{"rustc": 15597765236515928571, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2241668132362809309, "path": 1247157282882620631, "deps": [[1988483478007900009, "unicode_ident", false, 7778369119110734958], [3060637413840920116, "proc_macro2", false, 10112063138115658178], [17990358020177143287, "quote", false, 7404307475886686990]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-87de02df14de115c/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}