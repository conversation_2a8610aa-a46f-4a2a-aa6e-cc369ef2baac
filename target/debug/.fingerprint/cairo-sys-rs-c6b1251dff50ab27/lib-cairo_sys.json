{"rustc": 15597765236515928571, "features": "[\"glib\", \"use_glib\"]", "declared_features": "[\"freetype\", \"glib\", \"pdf\", \"png\", \"ps\", \"script\", \"svg\", \"use_glib\", \"v1_16\", \"v1_18\", \"win32-surface\", \"winapi\", \"x11\", \"xcb\", \"xlib\"]", "target": 12604004911878344227, "profile": 15657897354478470176, "path": 13365569039305138988, "deps": [[2924422107542798392, "libc", false, 4647260894900377729], [6885242093860886281, "build_script_build", false, 50331331620320231], [13626264195287554611, "glib", false, 16444580578771806746]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/cairo-sys-rs-c6b1251dff50ab27/dep-lib-cairo_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}