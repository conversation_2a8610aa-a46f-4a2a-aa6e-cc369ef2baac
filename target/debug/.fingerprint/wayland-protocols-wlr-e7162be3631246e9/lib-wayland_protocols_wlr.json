{"rustc": 15597765236515928571, "features": "[\"client\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"wayland-client\", \"wayland-server\"]", "target": 10385440283939368274, "profile": 15657897354478470176, "path": 2319904988488742703, "deps": [[7896293946984509699, "bitflags", false, 14392917110399809528], [14820987988873142214, "wayland_protocols", false, 4785928541821906293], [17553607095298811701, "wayland_client", false, 5691400987117699673], [17916187082124099642, "wayland_backend", false, 1478301405608795034], [17920034471148732551, "wayland_scanner", false, 1304274256025628772]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-wlr-e7162be3631246e9/dep-lib-wayland_protocols_wlr", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}