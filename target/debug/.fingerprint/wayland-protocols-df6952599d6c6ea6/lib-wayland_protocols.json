{"rustc": 15597765236515928571, "features": "[\"client\", \"staging\", \"unstable\", \"wayland-client\"]", "declared_features": "[\"client\", \"server\", \"staging\", \"unstable\", \"wayland-client\", \"wayland-server\"]", "target": 1390344349254038120, "profile": 15657897354478470176, "path": 18142966261599559032, "deps": [[7896293946984509699, "bitflags", false, 14392917110399809528], [17553607095298811701, "wayland_client", false, 5691400987117699673], [17916187082124099642, "wayland_backend", false, 1478301405608795034], [17920034471148732551, "wayland_scanner", false, 1304274256025628772]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wayland-protocols-df6952599d6c6ea6/dep-lib-wayland_protocols", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}