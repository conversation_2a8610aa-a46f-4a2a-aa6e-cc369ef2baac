{"rustc": 15597765236515928571, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 15657897354478470176, "path": 10459481310629798056, "deps": [[4675849561795547236, "miniz_oxide", false, 3445848385781817136], [5466618496199522463, "crc32fast", false, 6580319735379768419]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/flate2-ff3627791065cf0a/dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}