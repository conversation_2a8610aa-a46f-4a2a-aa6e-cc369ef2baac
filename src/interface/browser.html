<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TECH-BROWSER - Professional Web Browser</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1e3a8a;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Barre d'onglets */
        .tabs-bar {
            background: #1e40af;
            padding: 8px 12px 0;
            display: flex;
            align-items: end;
            min-height: 40px;
        }

        .tab {
            background: #1e40af;
            color: #94a3b8;
            padding: 8px 16px;
            border-radius: 8px 8px 0 0;
            margin-right: 4px;
            cursor: pointer;
            transition: all 0.2s;
            border: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 200px;
        }

        .tab.active {
            background: #2563eb;
            color: white;
        }

        .tab:hover:not(.active) {
            background: #2563eb;
            color: #e2e8f0;
        }

        .tab-close {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            padding: 2px;
            border-radius: 2px;
            font-size: 12px;
        }

        .tab-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .new-tab {
            background: none;
            border: none;
            color: #94a3b8;
            padding: 8px;
            cursor: pointer;
            font-size: 18px;
            border-radius: 4px;
        }

        .new-tab:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        /* Barre de navigation */
        .nav-bar {
            background: #2563eb;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .nav-button {
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            transition: all 0.2s;
        }

        .nav-button:hover {
            background: rgba(255,255,255,0.2);
        }

        .nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .address-bar {
            flex: 1;
            background: rgba(255,255,255,0.95);
            border: none;
            padding: 10px 16px;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .address-bar:focus {
            background: white;
            box-shadow: 0 2px 12px rgba(0,0,0,0.2);
        }

        .security-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #10b981;
            font-size: 14px;
        }

        .address-container {
            position: relative;
            flex: 1;
        }

        .toolbar-buttons {
            display: flex;
            gap: 8px;
        }

        .toolbar-button {
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .toolbar-button:hover {
            background: rgba(255,255,255,0.2);
        }

        /* Zone de contenu web */
        .web-content {
            flex: 1;
            background: white;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .web-placeholder {
            text-align: center;
            padding: 40px;
            max-width: 600px;
        }

        .web-placeholder h2 {
            color: #1e40af;
            margin-bottom: 16px;
            font-size: 24px;
        }

        .web-placeholder p {
            margin-bottom: 12px;
            color: #64748b;
            line-height: 1.5;
        }

        .nav-demo-button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 8px;
            transition: all 0.2s;
        }

        .nav-demo-button:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .demo-urls {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
            margin-top: 20px;
        }

        /* Barre de favoris */
        .bookmarks-bar {
            background: #3b82f6;
            padding: 8px 12px;
            display: flex;
            gap: 12px;
            min-height: 36px;
            align-items: center;
        }

        .bookmark {
            color: white;
            text-decoration: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 13px;
            transition: all 0.2s;
        }

        .bookmark:hover {
            background: rgba(255,255,255,0.1);
        }

        /* Loading indicator */
        .loading {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: #10b981;
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s;
        }

        .loading.active {
            transform: scaleX(1);
        }
    </style>
</head>
<body>
    <!-- Barre d'onglets -->
    <div class="tabs-bar">
        <div class="tab active" data-tab="0">
            <span class="tab-title">Nouvel onglet</span>
            <button class="tab-close" onclick="closeTab(0)">×</button>
        </div>
        <button class="new-tab" onclick="newTab()">+</button>
    </div>

    <!-- Barre de navigation -->
    <div class="nav-bar">
        <button class="nav-button" id="back-btn" onclick="goBack()" disabled>←</button>
        <button class="nav-button" id="forward-btn" onclick="goForward()" disabled>→</button>
        <button class="nav-button" id="refresh-btn" onclick="refresh()">⟳</button>
        
        <div class="address-container">
            <span class="security-icon">🔒</span>
            <input type="text" class="address-bar" id="address-bar" 
                   placeholder="Rechercher ou saisir une adresse web" 
                   value="https://www.google.com"
                   onkeypress="handleAddressEnter(event)">
        </div>

        <div class="toolbar-buttons">
            <button class="toolbar-button" onclick="showInterface()" title="Retour interface">🏠</button>
            <button class="toolbar-button">📥</button>
            <button class="toolbar-button">🧩</button>
            <button class="toolbar-button">🛡️ 0</button>
            <button class="toolbar-button">⚙️</button>
        </div>
    </div>

    <!-- Barre de favoris -->
    <div class="bookmarks-bar">
        <a href="#" class="bookmark" onclick="navigate('https://www.google.com')">Google</a>
        <a href="#" class="bookmark" onclick="navigate('https://www.youtube.com')">YouTube</a>
        <a href="#" class="bookmark" onclick="navigate('https://github.com')">GitHub</a>
        <a href="#" class="bookmark" onclick="navigate('https://stackoverflow.com')">Stack Overflow</a>
    </div>

    <!-- Zone de contenu web -->
    <div class="web-content">
        <div class="loading" id="loading"></div>
        <div class="web-placeholder" id="web-placeholder">
            <div>
                <h2>🚀 TECH-BROWSER</h2>
                <p><strong>Navigateur avec interface permanente + fenêtre de contenu !</strong></p>
                <p>✅ Interface permanente (cette fenêtre)</p>
                <p>✅ Fenêtre de contenu séparée (s'ouvre lors de la navigation)</p>
                <p>✅ WebView dual sans iframe</p>
                <p>✅ Communication JavaScript ↔ Rust</p>

                <div class="demo-urls">
                    <button class="nav-demo-button" onclick="navigateToSite('https://www.google.com')">
                        🔍 Google
                    </button>
                    <button class="nav-demo-button" onclick="navigateToSite('https://www.youtube.com')">
                        📺 YouTube
                    </button>
                    <button class="nav-demo-button" onclick="navigateToSite('https://github.com')">
                        💻 GitHub
                    </button>
                </div>

                <p style="margin-top: 20px;"><em>Cliquez sur un site → Fenêtre de contenu s'ouvre</em></p>
                <p><em>Bouton 🏠 → Ferme la fenêtre de contenu</em></p>
                <p><strong>Phase 5.3 :</strong> WebView dual - Interface + Contenu</p>
            </div>
        </div>
    </div>

    <script>
        // Communication avec Rust via IPC
        let currentUrl = 'https://www.google.com';
        let tabs = [{ id: 0, title: 'Google', url: 'https://www.google.com' }];
        let activeTab = 0;

        // Navigation
        function navigate(url) {
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                // Si ce n'est pas une URL, faire une recherche Google
                if (!url.includes('.')) {
                    url = 'https://www.google.com/search?q=' + encodeURIComponent(url);
                } else {
                    url = 'https://' + url;
                }
            }

            currentUrl = url;
            document.getElementById('address-bar').value = url;

            console.log('🌐 Navigation vers:', url);

            // Envoyer commande à Rust via IPC pour navigation WebView
            if (window.ipc) {
                window.ipc.postMessage(JSON.stringify({
                    cmd: 'navigate',
                    url: url
                }));

                showLoading();
                updateTabTitle('Chargement...');

                // Cacher le placeholder car on va naviguer dans le WebView principal
                hideInterface();
            } else {
                console.log('📨 IPC non disponible - Mode test');
                // En mode test, on simule
                showLoading();
                updateTabTitle('Chargement...');

                setTimeout(() => {
                    updateTabTitle('Navigation simulée');
                    document.getElementById('loading').classList.remove('active');
                }, 2000);
            }
        }

        function navigateToSite(url) {
            console.log('🌐 Navigation directe vers:', url);

            // Mettre à jour l'interface
            document.getElementById('address-bar').value = url;
            currentUrl = url;
            showLoading();
            updateTabTitle('Chargement...');

            // Envoyer commande de navigation au WebView principal
            if (window.ipc) {
                window.ipc.postMessage(JSON.stringify({
                    cmd: 'navigate_webview',
                    url: url
                }));
            } else {
                // Mode simulation
                setTimeout(() => {
                    updateTabTitle('Site chargé (simulation)');
                    document.getElementById('loading').classList.remove('active');

                    // Afficher un message de succès
                    const placeholder = document.getElementById('web-placeholder');
                    placeholder.innerHTML = `
                        <div>
                            <h2>✅ Navigation Simulée</h2>
                            <p><strong>URL :</strong> ${url}</p>
                            <p>Dans un vrai navigateur, cette page s'afficherait ici</p>
                            <button class="nav-demo-button" onclick="location.reload()">
                                🔄 Retour à l'interface
                            </button>
                        </div>
                    `;
                }, 1500);
            }
        }

        function hideInterface() {
            // Cacher l'interface pour laisser place au contenu web
            document.body.style.display = 'none';
        }

        function showInterface() {
            console.log('🏠 Retour à l\'interface');

            // Envoyer commande pour revenir à l'interface
            if (window.ipc) {
                window.ipc.postMessage(JSON.stringify({
                    cmd: 'show_interface'
                }));
            } else {
                // Recharger l'interface
                location.reload();
            }
        }

        // Fonction supprimée - iframe ne fonctionne pas avec WebView2

        function goBack() {
            console.log('⬅️ Retour demandé');
            if (window.ipc) {
                window.ipc.postMessage(JSON.stringify({ cmd: 'go_back' }));
            } else {
                console.log('⬅️ Retour (simulation)');
            }
        }

        function goForward() {
            console.log('➡️ Avancer demandé');
            if (window.ipc) {
                window.ipc.postMessage(JSON.stringify({ cmd: 'go_forward' }));
            } else {
                console.log('➡️ Avancer (simulation)');
            }
        }

        function refresh() {
            console.log('🔄 Actualisation demandée');
            if (window.ipc) {
                window.ipc.postMessage(JSON.stringify({ cmd: 'refresh' }));
            } else {
                console.log('🔄 Actualisation (simulation)');
                location.reload(); // Recharger l'interface
            }
            showLoading();
        }

        function handleAddressEnter(event) {
            if (event.key === 'Enter') {
                const url = event.target.value;
                navigate(url);
            }
        }

        // Gestion des onglets
        function newTab() {
            const newId = tabs.length;
            tabs.push({ id: newId, title: 'Nouvel onglet', url: 'https://www.google.com' });
            
            // Créer l'onglet visuellement
            const tabsBar = document.querySelector('.tabs-bar');
            const newTabBtn = tabsBar.querySelector('.new-tab');
            
            const tab = document.createElement('div');
            tab.className = 'tab';
            tab.dataset.tab = newId;
            tab.innerHTML = `
                <span class="tab-title">Nouvel onglet</span>
                <button class="tab-close" onclick="closeTab(${newId})">×</button>
            `;
            tab.onclick = () => switchTab(newId);
            
            tabsBar.insertBefore(tab, newTabBtn);
            switchTab(newId);
        }

        function closeTab(tabId) {
            if (tabs.length === 1) return; // Garder au moins un onglet
            
            tabs = tabs.filter(tab => tab.id !== tabId);
            document.querySelector(`[data-tab="${tabId}"]`).remove();
            
            if (activeTab === tabId) {
                switchTab(tabs[0].id);
            }
        }

        function switchTab(tabId) {
            // Mettre à jour l'interface
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
            
            activeTab = tabId;
            const tab = tabs.find(t => t.id === tabId);
            if (tab) {
                navigate(tab.url);
            }
        }

        function updateTabTitle(title) {
            const activeTabElement = document.querySelector('.tab.active .tab-title');
            if (activeTabElement) {
                activeTabElement.textContent = title.length > 20 ? title.substring(0, 20) + '...' : title;
            }
            
            // Mettre à jour dans le tableau
            const tab = tabs.find(t => t.id === activeTab);
            if (tab) {
                tab.title = title;
            }
        }

        // Loading indicator
        function showLoading() {
            document.getElementById('loading').classList.add('active');
            setTimeout(() => {
                document.getElementById('loading').classList.remove('active');
            }, 2000);
        }

        // Initialisation IPC
        function initializeIPC() {
            // Vérifier si IPC est disponible
            if (window.ipc) {
                console.log('✅ IPC disponible - Communication Rust ↔ JS active');
            } else {
                console.log('⚠️ IPC non disponible - Mode simulation');
                // Créer un mock IPC pour les tests
                window.ipc = {
                    postMessage: function(message) {
                        console.log('📨 Mock IPC:', message);
                    }
                };
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 TECH-BROWSER Interface chargée');

            // Initialiser IPC
            initializeIPC();

            // Configuration interface (iframe supprimé)

            // Simuler le chargement initial
            setTimeout(() => {
                updateTabTitle('TECH-BROWSER');
                // Pas de updateWebContent ici, on garde le placeholder
            }, 500);

            // Test de communication IPC
            setTimeout(() => {
                console.log('🧪 Test IPC...');
                if (window.ipc) {
                    window.ipc.postMessage(JSON.stringify({
                        cmd: 'test',
                        message: 'Interface JavaScript prête'
                    }));
                }
            }, 1000);
        });
    </script>
</body>
</html>
