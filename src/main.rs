// Désactive la console Windows pour une vraie application GUI
#![cfg_attr(target_os = "windows", windows_subsystem = "windows")]

use log::info;
use std::sync::{Arc, Mutex};

use tao::{
    event::{Event, WindowEvent, ElementState, KeyEvent},
    event_loop::{ControlFlow, EventLoop},
    window::WindowBuilder,
    keyboard::Key,
    dpi::{LogicalPosition, LogicalSize},
};

use wry::{
    WebViewBuilder, WebView,
};

mod config;
use config::BrowserConfig;

// Structure pour gérer les WebViews
struct BrowserManager {
    interface_webview: WebView,
    content_webview: Option<WebView>,
    current_url: Arc<Mutex<String>>,
}

impl BrowserManager {
    fn new(interface_webview: WebView) -> Self {
        Self {
            interface_webview,
            content_webview: None,
            current_url: Arc::new(Mutex::new("about:blank".to_string())),
        }
    }

    fn navigate_to(&mut self, url: &str, window: &tao::window::Window) -> Result<(), Box<dyn std::error::Error>> {
        info!("🌐 Navigation WebView principal vers: {}", url);

        // Pour l'instant, utilisons la navigation directe du WebView interface
        // TODO: Implémenter WebView séparé quand wry le supportera mieux

        // Naviguer directement dans le WebView principal
        self.interface_webview.load_url(url)?;

        // Mettre à jour l'URL courante
        if let Ok(mut current) = self.current_url.lock() {
            *current = url.to_string();
        }

        info!("✅ Navigation effectuée vers: {}", url);
        Ok(())
    }
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialisation du logging
    env_logger::init();
    info!("🚀 Démarrage TECH-BROWSER - Navigateur Ultra Moderne avec Wry");

    // Chargement de la configuration
    let _config = BrowserConfig::load();
    info!("📋 Configuration chargée");

    // Création de l'event loop
    let event_loop = EventLoop::new();

    // Création de la fenêtre principale
    let window = WindowBuilder::new()
        .with_title("TECH-BROWSER - Professional Web Browser")
        .with_inner_size(tao::dpi::LogicalSize::new(1400, 900))
        .with_min_inner_size(tao::dpi::LogicalSize::new(800, 600))
        .build(&event_loop)
        .map_err(|e| format!("Erreur création fenêtre: {}", e))?;

    info!("✅ Fenêtre créée: {}x{}", 1400, 900);

    // Créer le gestionnaire de navigateur
    let browser_manager = Arc::new(Mutex::new(None::<BrowserManager>));
    let browser_manager_clone = browser_manager.clone();
    let window_clone = Arc::new(window);
    let window_for_ipc = window_clone.clone();

    // Création du WebView principal avec interface
    let interface_html = include_str!("interface/browser.html");
    let interface_webview = WebViewBuilder::new()
        .with_html(interface_html)
        .with_ipc_handler(move |request| {
            let body = request.body();
            info!("📨 Message IPC reçu: {}", body);

            // Parser le message JSON
            if let Ok(message) = serde_json::from_str::<serde_json::Value>(body) {
                if let Some(cmd) = message.get("cmd").and_then(|v| v.as_str()) {
                    match cmd {
                        "navigate" | "navigate_webview" => {
                            if let Some(url) = message.get("url").and_then(|v| v.as_str()) {
                                info!("🌐 Navigation demandée vers: {}", url);

                                // Accéder au gestionnaire de navigateur
                                if let Ok(mut manager_opt) = browser_manager_clone.lock() {
                                    if let Some(ref mut manager) = manager_opt.as_mut() {
                                        if let Err(e) = manager.navigate_to(url, &window_for_ipc) {
                                            info!("❌ Erreur navigation: {}", e);
                                        }
                                    } else {
                                        info!("⚠️ Gestionnaire navigateur non initialisé");
                                    }
                                }
                            }
                        }
                        "go_back" => {
                            info!("⬅️ Retour demandé");
                            // TODO: Implémenter retour dans WebView contenu
                        }
                        "go_forward" => {
                            info!("➡️ Avancer demandé");
                            // TODO: Implémenter avancer dans WebView contenu
                        }
                        "refresh" => {
                            info!("🔄 Actualisation demandée");
                            // TODO: Actualiser WebView contenu
                        }
                        "show_interface" => {
                            info!("🏠 Retour à l'interface demandé");

                            // Recharger l'interface HTML
                            if let Ok(mut manager_opt) = browser_manager_clone.lock() {
                                if let Some(ref mut manager) = manager_opt.as_mut() {
                                    let interface_html = include_str!("interface/browser.html");
                                    if let Err(e) = manager.interface_webview.load_html(interface_html) {
                                        info!("❌ Erreur rechargement interface: {}", e);
                                    }
                                }
                            }
                        }
                        "test" => {
                            if let Some(msg) = message.get("message").and_then(|v| v.as_str()) {
                                info!("🧪 Test IPC reçu: {}", msg);
                            }
                        }
                        _ => {
                            info!("❓ Commande inconnue: {}", cmd);
                        }
                    }
                }
            }
        })
        .build(&window_clone)
        .map_err(|e| format!("Erreur création WebView interface: {}", e))?;

    info!("🎯 WebView interface créé avec succès");

    // Initialiser le gestionnaire de navigateur
    {
        let mut manager_opt = browser_manager.lock().unwrap();
        *manager_opt = Some(BrowserManager::new(interface_webview));
    }

    info!("🎨 Interface navigateur avec onglets chargée");
    info!("🚀 Gestionnaire WebView dual initialisé");

    // Boucle d'événements
    event_loop.run(move |event, _, control_flow| {
        *control_flow = ControlFlow::Wait;

        match event {
            Event::WindowEvent {
                event: WindowEvent::CloseRequested,
                ..
            } => {
                info!("🔄 Fermeture TECH-BROWSER demandée");
                *control_flow = ControlFlow::Exit;
            }
            Event::WindowEvent {
                event: WindowEvent::Resized(size),
                ..
            } => {
                info!("📐 Redimensionnement: {}x{}", size.width, size.height);
                // Le WebView se redimensionne automatiquement
            }
            Event::WindowEvent {
                event: WindowEvent::KeyboardInput {
                    event: KeyEvent { logical_key, state, .. },
                    ..
                },
                ..
            } => {
                // Gestion des raccourcis clavier
                if state == ElementState::Pressed {
                    // TODO: Implémenter raccourcis avec gestionnaire
                    info!("⌨️ Raccourci clavier: {:?}", logical_key);
                }
            }
            _ => {}
        }
    });

    Ok(())
}

fn handle_keyboard_input(key: &Key, webview: &WebView) {
    // Conversion simple en string pour comparaison
    let key_str = format!("{:?}", key);

    if key_str.contains("F5") {
        // F5 - Actualiser
        info!("🔄 F5 - Actualisation page");
        let _ = webview.evaluate_script("window.location.reload();");
    } else if key_str.contains("F12") {
        // F12 - DevTools (si disponible)
        info!("🔧 F12 - DevTools");
        #[cfg(debug_assertions)]
        {
            let _ = webview.open_devtools();
        }
    } else if key_str.contains("Escape") {
        // ESC - Arrêter le chargement
        info!("⏹️ ESC - Arrêt chargement");
        // Note: wry ne supporte pas stop() directement
    }
}
