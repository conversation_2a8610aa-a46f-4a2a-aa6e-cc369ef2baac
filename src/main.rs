// Désactive la console Windows pour une vraie application GUI
#![cfg_attr(target_os = "windows", windows_subsystem = "windows")]

use log::info;

use tao::{
    event::{Event, WindowEvent, ElementState, KeyEvent},
    event_loop::{ControlFlow, EventLoop},
    window::WindowBuilder,
    keyboard::Key,
};

use wry::{
    WebViewBuilder, WebView,
};

mod config;
use config::BrowserConfig;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialisation du logging
    env_logger::init();
    info!("🚀 Démarrage TECH-BROWSER - Navigateur Ultra Moderne avec Wry");

    // Chargement de la configuration
    let _config = BrowserConfig::load();
    info!("📋 Configuration chargée");

    // Création de l'event loop
    let event_loop = EventLoop::new();

    // Création de la fenêtre principale
    let window = WindowBuilder::new()
        .with_title("TECH-BROWSER - Professional Web Browser")
        .with_inner_size(tao::dpi::LogicalSize::new(1400, 900))
        .with_min_inner_size(tao::dpi::LogicalSize::new(800, 600))
        .build(&event_loop)
        .map_err(|e| format!("Erreur création fenêtre: {}", e))?;

    info!("✅ Fenêtre créée: {}x{}", 1400, 900);

    // Création du WebView avec notre interface navigateur
    let interface_html = include_str!("interface/browser.html");
    let webview = WebViewBuilder::new()
        .with_html(interface_html)
        .with_ipc_handler(|request| {
            let body = request.body();
            info!("📨 Message IPC reçu: {}", body);

            // Parser le message JSON
            if let Ok(message) = serde_json::from_str::<serde_json::Value>(body) {
                if let Some(cmd) = message.get("cmd").and_then(|v| v.as_str()) {
                    match cmd {
                        "navigate" => {
                            if let Some(url) = message.get("url").and_then(|v| v.as_str()) {
                                info!("🌐 Navigation demandée vers: {}", url);
                                // TODO: Implémenter navigation réelle
                            }
                        }
                        "go_back" => {
                            info!("⬅️ Retour demandé");
                            // TODO: Implémenter retour
                        }
                        "go_forward" => {
                            info!("➡️ Avancer demandé");
                            // TODO: Implémenter avancer
                        }
                        "refresh" => {
                            info!("🔄 Actualisation demandée");
                            // TODO: Implémenter actualisation
                        }
                        "test" => {
                            if let Some(msg) = message.get("message").and_then(|v| v.as_str()) {
                                info!("🧪 Test IPC reçu: {}", msg);
                            }
                        }
                        _ => {
                            info!("❓ Commande inconnue: {}", cmd);
                        }
                    }
                }
            }
        })
        .with_navigation_handler(|url| {
            info!("🌐 Navigation vers: {}", url);
            true // Autoriser toutes les navigations
        })
        .with_new_window_req_handler(|url| {
            info!("🔗 Nouvelle fenêtre demandée: {}", url);
            false // Ouvrir dans la même fenêtre pour l'instant
        })
        .build(&window)
        .map_err(|e| format!("Erreur création WebView: {}", e))?;

    info!("🎯 WebView créé avec succès");
    info!("🎨 Interface navigateur avec onglets chargée");

    // Boucle d'événements
    event_loop.run(move |event, _, control_flow| {
        *control_flow = ControlFlow::Wait;

        match event {
            Event::WindowEvent {
                event: WindowEvent::CloseRequested,
                ..
            } => {
                info!("🔄 Fermeture TECH-BROWSER demandée");
                *control_flow = ControlFlow::Exit;
            }
            Event::WindowEvent {
                event: WindowEvent::Resized(size),
                ..
            } => {
                info!("📐 Redimensionnement: {}x{}", size.width, size.height);
                // Le WebView se redimensionne automatiquement
            }
            Event::WindowEvent {
                event: WindowEvent::KeyboardInput {
                    event: KeyEvent { logical_key, state, .. },
                    ..
                },
                ..
            } => {
                // Gestion des raccourcis clavier
                if state == ElementState::Pressed {
                    handle_keyboard_input(&logical_key, &webview);
                }
            }
            _ => {}
        }
    });

    Ok(())
}

fn handle_keyboard_input(key: &Key, webview: &WebView) {
    // Conversion simple en string pour comparaison
    let key_str = format!("{:?}", key);

    if key_str.contains("F5") {
        // F5 - Actualiser
        info!("🔄 F5 - Actualisation page");
        let _ = webview.evaluate_script("window.location.reload();");
    } else if key_str.contains("F12") {
        // F12 - DevTools (si disponible)
        info!("🔧 F12 - DevTools");
        #[cfg(debug_assertions)]
        {
            let _ = webview.open_devtools();
        }
    } else if key_str.contains("Escape") {
        // ESC - Arrêter le chargement
        info!("⏹️ ESC - Arrêt chargement");
        // Note: wry ne supporte pas stop() directement
    }
}
