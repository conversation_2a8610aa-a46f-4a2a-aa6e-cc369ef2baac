import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/core";
import "./App.css";

// Types
interface Tab {
  id: string;
  title: string;
  url: string;
  is_loading: boolean;
  favicon?: string;
  created_at: number;
}

interface BrowserState {
  tabs: Tab[];
  active_tab_id?: string;
}

interface Favorite {
  id: string;
  title: string;
  url: string;
  favicon?: string;
}

function App() {
  const [browserState, setBrowserState] = useState<BrowserState>({ tabs: [] });
  const [favorites, setFavorites] = useState<Favorite[]>([]);
  const [addressBarValue, setAddressBarValue] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  // Initialize browser
  useEffect(() => {
    initializeBrowser();
  }, []);

  const initializeBrowser = async () => {
    try {
      await invoke("init_browser");
      await loadBrowserState();
      await loadFavorites();
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to initialize browser:", error);
      setIsLoading(false);
    }
  };

  const loadBrowserState = async () => {
    try {
      const state = await invoke<BrowserState>("get_browser_state");
      setBrowserState(state);

      // Update address bar with active tab URL
      const activeTab = state.tabs.find(tab => tab.id === state.active_tab_id);
      if (activeTab) {
        setAddressBarValue(activeTab.url);
      }
    } catch (error) {
      console.error("Failed to load browser state:", error);
    }
  };

  const loadFavorites = async () => {
    try {
      const favs = await invoke<Favorite[]>("get_favorites");
      setFavorites(favs);
    } catch (error) {
      console.error("Failed to load favorites:", error);
    }
  };

  const createTab = async (url?: string) => {
    try {
      await invoke("create_tab", { url });
      await loadBrowserState();
    } catch (error) {
      console.error("Failed to create tab:", error);
    }
  };

  const closeTab = async (tabId: string) => {
    try {
      await invoke("close_tab", { tab_id: tabId });
      await loadBrowserState();
    } catch (error) {
      console.error("Failed to close tab:", error);
    }
  };

  const setActiveTab = async (tabId: string) => {
    try {
      await invoke("set_active_tab", { tab_id: tabId });
      await loadBrowserState();
    } catch (error) {
      console.error("Failed to set active tab:", error);
    }
  };

  const navigate = async (url: string) => {
    if (!browserState.active_tab_id) return;

    try {
      await invoke("navigate", {
        tab_id: browserState.active_tab_id,
        url
      });
      await loadBrowserState();
    } catch (error) {
      console.error("Failed to navigate:", error);
    }
  };

  const goBack = async () => {
    if (!browserState.active_tab_id) return;

    try {
      await invoke("go_back", { tab_id: browserState.active_tab_id });
      await loadBrowserState();
    } catch (error) {
      console.error("Failed to go back:", error);
    }
  };

  const goForward = async () => {
    if (!browserState.active_tab_id) return;

    try {
      await invoke("go_forward", { tab_id: browserState.active_tab_id });
      await loadBrowserState();
    } catch (error) {
      console.error("Failed to go forward:", error);
    }
  };

  const reload = async () => {
    if (!browserState.active_tab_id) return;

    try {
      await invoke("reload", { tab_id: browserState.active_tab_id });
      await loadBrowserState();
    } catch (error) {
      console.error("Failed to reload:", error);
    }
  };

  const handleAddressBarSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (addressBarValue.trim()) {
      navigate(addressBarValue.trim());
    }
  };

  const handleFavoriteClick = (url: string) => {
    setAddressBarValue(url);
    navigate(url);
  };

  if (isLoading) {
    return (
      <div className="loading-screen">
        <h1>🚀 TECH-BROWSER</h1>
        <p>Initializing ultra-modern browser...</p>
      </div>
    );
  }

  const activeTab = browserState.tabs.find(tab => tab.id === browserState.active_tab_id);

  return (
    <div className="browser-container">
      {/* Tab Bar */}
      <div className="tab-bar">
        {browserState.tabs.map((tab) => (
          <div
            key={tab.id}
            className={`tab ${tab.id === browserState.active_tab_id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-title">
              {tab.is_loading ? "⏳" : "📄"} {tab.title}
            </span>
            {browserState.tabs.length > 1 && (
              <button
                className="tab-close"
                onClick={(e) => {
                  e.stopPropagation();
                  closeTab(tab.id);
                }}
              >
                ×
              </button>
            )}
          </div>
        ))}
        <button className="new-tab-btn" onClick={() => createTab()}>
          +
        </button>
      </div>

      {/* Navigation Bar */}
      <div className="nav-bar">
        <div className="nav-controls">
          <button onClick={goBack} title="Retour">←</button>
          <button onClick={goForward} title="Avancer">→</button>
          <button onClick={reload} title="Actualiser">⟳</button>
        </div>

        <form className="address-bar-form" onSubmit={handleAddressBarSubmit}>
          <div className="address-bar">
            <span className="security-icon">🔒</span>
            <input
              type="text"
              value={addressBarValue}
              onChange={(e) => setAddressBarValue(e.target.value)}
              placeholder="Rechercher ou saisir une URL..."
              className="address-input"
            />
          </div>
        </form>

        <div className="browser-controls">
          <button title="Téléchargements">📥</button>
          <button title="Extensions">🧩</button>
          <button title="Bloqueur de pub">🛡️</button>
          <button title="Paramètres">⚙️</button>
        </div>
      </div>

      {/* Favorites Bar */}
      <div className="favorites-bar">
        {favorites.map((favorite) => (
          <button
            key={favorite.id}
            className="favorite-btn"
            onClick={() => handleFavoriteClick(favorite.url)}
            title={favorite.title}
          >
            {favorite.favicon} {favorite.title}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div className="content-area">
        {activeTab ? (
          <div className="web-content">
            {activeTab.url === "tech://start" ? (
              <div className="start-page">
                <h1>🚀 TECH-BROWSER</h1>
                <p>Navigateur ultra-moderne avec Rust + Servo + Tauri + React</p>
                <div className="start-features">
                  <div className="feature">✅ Interface native moderne</div>
                  <div className="feature">✅ Navigation web rapide</div>
                  <div className="feature">🔧 Moteur Servo (en développement)</div>
                  <div className="feature">🔒 Privacy-first design</div>
                </div>
                <p>Cliquez sur un favori ou saisissez une URL pour commencer !</p>
              </div>
            ) : (
              <div className="web-view-placeholder">
                <h2>🌐 Navigation vers:</h2>
                <p>{activeTab.url}</p>
                {activeTab.is_loading ? (
                  <div className="loading">⏳ Chargement...</div>
                ) : (
                  <div className="loaded">
                    <p>✅ Page chargée</p>
                    <p><strong>Note Phase 1:</strong> WebView sera intégré ici</p>
                    <p><strong>Phase 2:</strong> Moteur Servo remplacera WebView</p>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          <div className="no-tabs">
            <p>Aucun onglet ouvert</p>
            <button onClick={() => createTab()}>Créer un onglet</button>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
