/* TECH-BROWSER - Ultra-Modern Interface Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #0f0f0f;
  color: #ffffff;
  overflow: hidden;
}

#root {
  height: 100vh;
  width: 100vw;
}

/* Browser Container */
.browser-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f0f 100%);
}

/* Loading Screen */
.loading-screen {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f0f 100%);
}

.loading-screen h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4f46e5, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Tab Bar */
.tab-bar {
  display: flex;
  background: #1e1e2e;
  border-bottom: 1px solid #2a2a3a;
  padding: 0 8px;
  min-height: 40px;
  align-items: center;
}

.tab {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #2a2a3a;
  border: 1px solid #3a3a4a;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  margin-right: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 200px;
  min-width: 120px;
}

.tab:hover {
  background: #3a3a4a;
}

.tab.active {
  background: #4f46e5;
  border-color: #6366f1;
}

.tab-title {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.9rem;
}

.tab-close {
  background: none;
  border: none;
  color: #ffffff;
  cursor: pointer;
  padding: 2px 6px;
  margin-left: 8px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.tab-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.new-tab-btn {
  background: none;
  border: 1px solid #3a3a4a;
  color: #ffffff;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 1.2rem;
}

.new-tab-btn:hover {
  background: #3a3a4a;
  border-color: #4a4a5a;
}

/* Navigation Bar */
.nav-bar {
  display: flex;
  align-items: center;
  background: #1e1e2e;
  border-bottom: 1px solid #2a2a3a;
  padding: 8px 12px;
  gap: 12px;
}

.nav-controls {
  display: flex;
  gap: 4px;
}

.nav-controls button {
  background: #2a2a3a;
  border: 1px solid #3a3a4a;
  color: #ffffff;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.nav-controls button:hover {
  background: #3a3a4a;
  border-color: #4a4a5a;
}

.nav-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Address Bar */
.address-bar-form {
  flex: 1;
  max-width: 600px;
}

.address-bar {
  display: flex;
  align-items: center;
  background: #2a2a3a;
  border: 1px solid #3a3a4a;
  border-radius: 8px;
  padding: 0 12px;
  transition: all 0.2s ease;
}

.address-bar:focus-within {
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.security-icon {
  margin-right: 8px;
  font-size: 0.9rem;
}

.address-input {
  flex: 1;
  background: none;
  border: none;
  color: #ffffff;
  padding: 10px 0;
  font-size: 0.95rem;
  outline: none;
}

.address-input::placeholder {
  color: #888888;
}

/* Browser Controls */
.browser-controls {
  display: flex;
  gap: 4px;
}

.browser-controls button {
  background: #2a2a3a;
  border: 1px solid #3a3a4a;
  color: #ffffff;
  cursor: pointer;
  padding: 8px 10px;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.browser-controls button:hover {
  background: #3a3a4a;
  border-color: #4a4a5a;
}

/* Favorites Bar */
.favorites-bar {
  display: flex;
  background: #1a1a2a;
  border-bottom: 1px solid #2a2a3a;
  padding: 6px 12px;
  gap: 8px;
  overflow-x: auto;
}

.favorite-btn {
  background: none;
  border: 1px solid #3a3a4a;
  color: #ffffff;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-size: 0.9rem;
}

.favorite-btn:hover {
  background: #2a2a3a;
  border-color: #4a4a5a;
}

/* Content Area */
.content-area {
  flex: 1;
  background: #0f0f0f;
  overflow: hidden;
}

.web-content {
  height: 100%;
  width: 100%;
}

/* Start Page */
.start-page {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  padding: 2rem;
}

.start-page h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, #4f46e5, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.start-page p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: #cccccc;
}

.start-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
  max-width: 800px;
}

.feature {
  background: #1e1e2e;
  border: 1px solid #2a2a3a;
  border-radius: 8px;
  padding: 1rem;
  font-size: 1rem;
}

/* Web View Placeholder */
.web-view-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  padding: 2rem;
}

.web-view-placeholder h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: #4f46e5;
}

.web-view-placeholder p {
  font-size: 1.1rem;
  margin-bottom: 1rem;
  color: #cccccc;
}

.loading {
  font-size: 1.2rem;
  color: #06b6d4;
  animation: pulse 2s infinite;
}

.loaded {
  color: #10b981;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* No Tabs */
.no-tabs {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.no-tabs button {
  background: #4f46e5;
  border: none;
  color: #ffffff;
  cursor: pointer;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  margin-top: 1rem;
  transition: background 0.2s ease;
}

.no-tabs button:hover {
  background: #6366f1;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a2a;
}

::-webkit-scrollbar-thumb {
  background: #3a3a4a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4a4a5a;
}
