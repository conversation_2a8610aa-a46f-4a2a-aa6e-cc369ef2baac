// TECH-BROWSER - Browser Engine Module
// Phase 1: Basic web navigation with WebView (Servo integration planned for Phase 2)

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tauri::State;
use log::{info, warn, error};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Tab {
    pub id: String,
    pub title: String,
    pub url: String,
    pub is_loading: bool,
    pub favicon: Option<String>,
    pub created_at: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NavigationHistory {
    pub entries: Vec<String>,
    pub current_index: usize,
}

#[derive(Debug)]
pub struct BrowserEngine {
    pub tabs: Arc<Mutex<HashMap<String, Tab>>>,
    pub active_tab_id: Arc<Mutex<Option<String>>>,
    pub history: Arc<Mutex<HashMap<String, NavigationHistory>>>,
}

impl Default for BrowserEngine {
    fn default() -> Self {
        Self::new()
    }
}

impl BrowserEngine {
    pub fn new() -> Self {
        info!("🚀 Initializing TECH-BROWSER Engine");
        
        let mut tabs = HashMap::new();
        let initial_tab_id = uuid::Uuid::new_v4().to_string();
        
        // Create initial tab
        let initial_tab = Tab {
            id: initial_tab_id.clone(),
            title: "Nouvel onglet".to_string(),
            url: "tech://start".to_string(),
            is_loading: false,
            favicon: None,
            created_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };
        
        tabs.insert(initial_tab_id.clone(), initial_tab);
        
        let mut history = HashMap::new();
        history.insert(initial_tab_id.clone(), NavigationHistory {
            entries: vec!["tech://start".to_string()],
            current_index: 0,
        });
        
        Self {
            tabs: Arc::new(Mutex::new(tabs)),
            active_tab_id: Arc::new(Mutex::new(Some(initial_tab_id))),
            history: Arc::new(Mutex::new(history)),
        }
    }
    
    pub fn create_tab(&self, url: Option<String>) -> Result<String, String> {
        let tab_id = uuid::Uuid::new_v4().to_string();
        let url = url.unwrap_or_else(|| "tech://start".to_string());
        
        let tab = Tab {
            id: tab_id.clone(),
            title: "Chargement...".to_string(),
            url: url.clone(),
            is_loading: true,
            favicon: None,
            created_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs(),
        };
        
        // Add tab
        if let Ok(mut tabs) = self.tabs.lock() {
            tabs.insert(tab_id.clone(), tab);
        } else {
            return Err("Failed to lock tabs".to_string());
        }
        
        // Initialize history
        if let Ok(mut history) = self.history.lock() {
            history.insert(tab_id.clone(), NavigationHistory {
                entries: vec![url],
                current_index: 0,
            });
        }
        
        // Set as active tab
        if let Ok(mut active_tab_id) = self.active_tab_id.lock() {
            *active_tab_id = Some(tab_id.clone());
        }
        
        info!("📑 Created new tab: {}", tab_id);
        Ok(tab_id)
    }
    
    pub fn close_tab(&self, tab_id: &str) -> Result<(), String> {
        if let Ok(mut tabs) = self.tabs.lock() {
            if tabs.len() <= 1 {
                return Err("Cannot close last tab".to_string());
            }
            
            tabs.remove(tab_id);
            
            // If this was the active tab, switch to another
            if let Ok(mut active_tab_id) = self.active_tab_id.lock() {
                if active_tab_id.as_ref() == Some(&tab_id.to_string()) {
                    *active_tab_id = tabs.keys().next().map(|k| k.clone());
                }
            }
            
            // Remove history
            if let Ok(mut history) = self.history.lock() {
                history.remove(tab_id);
            }
            
            info!("❌ Closed tab: {}", tab_id);
            Ok(())
        } else {
            Err("Failed to lock tabs".to_string())
        }
    }
    
    pub fn navigate(&self, tab_id: &str, url: &str) -> Result<(), String> {
        info!("🌐 Navigating tab {} to: {}", tab_id, url);
        
        // Validate URL
        let validated_url = self.validate_url(url)?;
        
        // Update tab
        if let Ok(mut tabs) = self.tabs.lock() {
            if let Some(tab) = tabs.get_mut(tab_id) {
                tab.url = validated_url.clone();
                tab.is_loading = true;
                tab.title = "Chargement...".to_string();
            } else {
                return Err("Tab not found".to_string());
            }
        } else {
            return Err("Failed to lock tabs".to_string());
        }
        
        // Update history
        if let Ok(mut history) = self.history.lock() {
            if let Some(tab_history) = history.get_mut(tab_id) {
                // Remove forward history if navigating from middle
                tab_history.entries.truncate(tab_history.current_index + 1);
                tab_history.entries.push(validated_url);
                tab_history.current_index = tab_history.entries.len() - 1;
            }
        }
        
        // Simulate page load completion (in real implementation, this would be handled by Servo)
        self.simulate_page_load(tab_id, &validated_url);
        
        Ok(())
    }
    
    pub fn go_back(&self, tab_id: &str) -> Result<(), String> {
        if let Ok(mut history) = self.history.lock() {
            if let Some(tab_history) = history.get_mut(tab_id) {
                if tab_history.current_index > 0 {
                    tab_history.current_index -= 1;
                    let url = tab_history.entries[tab_history.current_index].clone();
                    
                    // Update tab without adding to history
                    if let Ok(mut tabs) = self.tabs.lock() {
                        if let Some(tab) = tabs.get_mut(tab_id) {
                            tab.url = url;
                            tab.is_loading = true;
                        }
                    }
                    
                    info!("⬅️ Going back in tab: {}", tab_id);
                    return Ok(());
                }
            }
        }
        
        Err("Cannot go back".to_string())
    }
    
    pub fn go_forward(&self, tab_id: &str) -> Result<(), String> {
        if let Ok(mut history) = self.history.lock() {
            if let Some(tab_history) = history.get_mut(tab_id) {
                if tab_history.current_index < tab_history.entries.len() - 1 {
                    tab_history.current_index += 1;
                    let url = tab_history.entries[tab_history.current_index].clone();
                    
                    // Update tab without adding to history
                    if let Ok(mut tabs) = self.tabs.lock() {
                        if let Some(tab) = tabs.get_mut(tab_id) {
                            tab.url = url;
                            tab.is_loading = true;
                        }
                    }
                    
                    info!("➡️ Going forward in tab: {}", tab_id);
                    return Ok(());
                }
            }
        }
        
        Err("Cannot go forward".to_string())
    }
    
    pub fn reload(&self, tab_id: &str) -> Result<(), String> {
        if let Ok(tabs) = self.tabs.lock() {
            if let Some(tab) = tabs.get(tab_id) {
                let url = tab.url.clone();
                drop(tabs); // Release lock before calling navigate
                return self.navigate(tab_id, &url);
            }
        }
        
        Err("Tab not found".to_string())
    }
    
    pub fn get_tabs(&self) -> Vec<Tab> {
        if let Ok(tabs) = self.tabs.lock() {
            tabs.values().cloned().collect()
        } else {
            vec![]
        }
    }
    
    pub fn get_active_tab_id(&self) -> Option<String> {
        if let Ok(active_tab_id) = self.active_tab_id.lock() {
            active_tab_id.clone()
        } else {
            None
        }
    }
    
    pub fn set_active_tab(&self, tab_id: &str) -> Result<(), String> {
        if let Ok(tabs) = self.tabs.lock() {
            if tabs.contains_key(tab_id) {
                if let Ok(mut active_tab_id) = self.active_tab_id.lock() {
                    *active_tab_id = Some(tab_id.to_string());
                    info!("🎯 Switched to tab: {}", tab_id);
                    return Ok(());
                }
            }
        }
        
        Err("Tab not found".to_string())
    }
    
    fn validate_url(&self, url: &str) -> Result<String, String> {
        // Handle special URLs
        if url.starts_with("tech://") {
            return Ok(url.to_string());
        }
        
        // Add protocol if missing
        let url = if !url.starts_with("http://") && !url.starts_with("https://") {
            if url.contains('.') && !url.contains(' ') {
                format!("https://{}", url)
            } else {
                // Search query
                format!("https://www.google.com/search?q={}", urlencoding::encode(url))
            }
        } else {
            url.to_string()
        };
        
        // Validate URL format
        match url::Url::parse(&url) {
            Ok(_) => Ok(url),
            Err(_) => Err("Invalid URL format".to_string()),
        }
    }
    
    fn simulate_page_load(&self, tab_id: &str, url: &str) {
        let tabs = self.tabs.clone();
        let tab_id = tab_id.to_string();
        let url = url.to_string();
        
        // Simulate async page loading
        tokio::spawn(async move {
            tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
            
            if let Ok(mut tabs) = tabs.lock() {
                if let Some(tab) = tabs.get_mut(&tab_id) {
                    tab.is_loading = false;
                    tab.title = extract_title_from_url(&url);
                }
            }
        });
    }
}

fn extract_title_from_url(url: &str) -> String {
    if url.starts_with("tech://start") {
        return "TECH-BROWSER".to_string();
    }
    
    if let Ok(parsed_url) = url::Url::parse(url) {
        if let Some(host) = parsed_url.host_str() {
            // Extract domain name
            let domain = host.replace("www.", "");
            let parts: Vec<&str> = domain.split('.').collect();
            if !parts.is_empty() {
                return parts[0].to_string().to_uppercase();
            }
        }
    }
    
    "Site Web".to_string()
}
