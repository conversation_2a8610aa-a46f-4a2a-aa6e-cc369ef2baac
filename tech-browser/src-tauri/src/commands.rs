// TECH-BROWSER - <PERSON>ri Commands
// Interface between <PERSON><PERSON> frontend and <PERSON><PERSON> backend

use crate::browser_engine::{BrowserEngine, Tab};
use tauri::State;
use serde::{Deserialize, Serialize};
use log::{info, error};

#[derive(Debug, Serialize, Deserialize)]
pub struct NavigateRequest {
    pub tab_id: String,
    pub url: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct TabRequest {
    pub tab_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreateTabRequest {
    pub url: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BrowserState {
    pub tabs: Vec<Tab>,
    pub active_tab_id: Option<String>,
}

// Get current browser state (tabs, active tab, etc.)
#[tauri::command]
pub async fn get_browser_state(
    engine: State<'_, BrowserEngine>,
) -> Result<BrowserState, String> {
    info!("📊 Getting browser state");
    
    let tabs = engine.get_tabs();
    let active_tab_id = engine.get_active_tab_id();
    
    Ok(BrowserState {
        tabs,
        active_tab_id,
    })
}

// Create a new tab
#[tauri::command]
pub async fn create_tab(
    request: CreateTabRequest,
    engine: State<'_, BrowserEngine>,
) -> Result<String, String> {
    info!("📑 Creating new tab with URL: {:?}", request.url);
    
    match engine.create_tab(request.url) {
        Ok(tab_id) => {
            info!("✅ Tab created successfully: {}", tab_id);
            Ok(tab_id)
        }
        Err(e) => {
            error!("❌ Failed to create tab: {}", e);
            Err(e)
        }
    }
}

// Close a tab
#[tauri::command]
pub async fn close_tab(
    request: TabRequest,
    engine: State<'_, BrowserEngine>,
) -> Result<(), String> {
    info!("❌ Closing tab: {}", request.tab_id);
    
    match engine.close_tab(&request.tab_id) {
        Ok(_) => {
            info!("✅ Tab closed successfully: {}", request.tab_id);
            Ok(())
        }
        Err(e) => {
            error!("❌ Failed to close tab: {}", e);
            Err(e)
        }
    }
}

// Navigate to URL
#[tauri::command]
pub async fn navigate(
    request: NavigateRequest,
    engine: State<'_, BrowserEngine>,
) -> Result<(), String> {
    info!("🌐 Navigating tab {} to: {}", request.tab_id, request.url);
    
    match engine.navigate(&request.tab_id, &request.url) {
        Ok(_) => {
            info!("✅ Navigation successful");
            Ok(())
        }
        Err(e) => {
            error!("❌ Navigation failed: {}", e);
            Err(e)
        }
    }
}

// Set active tab
#[tauri::command]
pub async fn set_active_tab(
    request: TabRequest,
    engine: State<'_, BrowserEngine>,
) -> Result<(), String> {
    info!("🎯 Setting active tab: {}", request.tab_id);
    
    match engine.set_active_tab(&request.tab_id) {
        Ok(_) => {
            info!("✅ Active tab set successfully");
            Ok(())
        }
        Err(e) => {
            error!("❌ Failed to set active tab: {}", e);
            Err(e)
        }
    }
}

// Go back in history
#[tauri::command]
pub async fn go_back(
    request: TabRequest,
    engine: State<'_, BrowserEngine>,
) -> Result<(), String> {
    info!("⬅️ Going back in tab: {}", request.tab_id);
    
    match engine.go_back(&request.tab_id) {
        Ok(_) => {
            info!("✅ Went back successfully");
            Ok(())
        }
        Err(e) => {
            error!("❌ Failed to go back: {}", e);
            Err(e)
        }
    }
}

// Go forward in history
#[tauri::command]
pub async fn go_forward(
    request: TabRequest,
    engine: State<'_, BrowserEngine>,
) -> Result<(), String> {
    info!("➡️ Going forward in tab: {}", request.tab_id);
    
    match engine.go_forward(&request.tab_id) {
        Ok(_) => {
            info!("✅ Went forward successfully");
            Ok(())
        }
        Err(e) => {
            error!("❌ Failed to go forward: {}", e);
            Err(e)
        }
    }
}

// Reload current page
#[tauri::command]
pub async fn reload(
    request: TabRequest,
    engine: State<'_, BrowserEngine>,
) -> Result<(), String> {
    info!("🔄 Reloading tab: {}", request.tab_id);
    
    match engine.reload(&request.tab_id) {
        Ok(_) => {
            info!("✅ Reload successful");
            Ok(())
        }
        Err(e) => {
            error!("❌ Failed to reload: {}", e);
            Err(e)
        }
    }
}

// Get favorites/bookmarks
#[tauri::command]
pub async fn get_favorites() -> Result<Vec<Favorite>, String> {
    info!("⭐ Getting favorites");
    
    // For Phase 1, return hardcoded favorites
    // In later phases, this will be loaded from storage
    Ok(vec![
        Favorite {
            id: "google".to_string(),
            title: "Google".to_string(),
            url: "https://www.google.com".to_string(),
            favicon: Some("🔍".to_string()),
        },
        Favorite {
            id: "youtube".to_string(),
            title: "YouTube".to_string(),
            url: "https://www.youtube.com".to_string(),
            favicon: Some("📺".to_string()),
        },
        Favorite {
            id: "github".to_string(),
            title: "GitHub".to_string(),
            url: "https://github.com".to_string(),
            favicon: Some("💻".to_string()),
        },
        Favorite {
            id: "stackoverflow".to_string(),
            title: "Stack Overflow".to_string(),
            url: "https://stackoverflow.com".to_string(),
            favicon: Some("📚".to_string()),
        },
    ])
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Favorite {
    pub id: String,
    pub title: String,
    pub url: String,
    pub favicon: Option<String>,
}

// Initialize browser engine
#[tauri::command]
pub async fn init_browser() -> Result<String, String> {
    info!("🚀 Initializing TECH-BROWSER");
    Ok("TECH-BROWSER initialized successfully".to_string())
}

// Get browser info
#[tauri::command]
pub async fn get_browser_info() -> Result<BrowserInfo, String> {
    Ok(BrowserInfo {
        name: "TECH-BROWSER".to_string(),
        version: "1.0.0".to_string(),
        engine: "WebView (Servo planned)".to_string(),
        features: vec![
            "Ultra-modern interface".to_string(),
            "Privacy-first design".to_string(),
            "Native ad-blocker (planned)".to_string(),
            "Sync encryption (planned)".to_string(),
        ],
    })
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BrowserInfo {
    pub name: String,
    pub version: String,
    pub engine: String,
    pub features: Vec<String>,
}
