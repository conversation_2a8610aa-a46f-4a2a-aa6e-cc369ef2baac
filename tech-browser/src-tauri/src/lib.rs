// TECH-BROWSER - Ultra-modern web browser
// Phase 1: Core browser functionality with <PERSON>ri + React

mod browser_engine;
mod commands;

use browser_engine::BrowserEngine;
use commands::*;
use log::info;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging
    env_logger::init();
    info!("🚀 Starting TECH-BROWSER v1.0.0");

    // Initialize browser engine
    let browser_engine = BrowserEngine::new();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .manage(browser_engine)
        .invoke_handler(tauri::generate_handler![
            init_browser,
            get_browser_info,
            get_browser_state,
            create_tab,
            close_tab,
            navigate,
            set_active_tab,
            go_back,
            go_forward,
            reload,
            get_favorites
        ])
        .run(tauri::generate_context!())
        .expect("error while running TECH-BROWSER");
}
