[package]
name = "tech-browser"
version = "1.0.0"
description = "TECH-BROWSER - Ultra-modern web browser with privacy and performance"
authors = ["TECH-BROWSER Team"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tech_browser_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.5", features = [] }

[dependencies]
# Tauri core
tauri = { version = "2.5", features = [] }
tauri-plugin-opener = "2.5"

# Serialization
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# SERVO ENGINE - Phase 1 Integration
servo = { git = "https://github.com/servo/servo", features = ["webgl", "webdriver"] }
servo-media = { git = "https://github.com/servo/media" }

# Core dependencies
url = "2.5"
tokio = { version = "1", features = ["full"] }
log = "0.4"
env_logger = "0.10"
uuid = { version = "1.0", features = ["v4"] }
urlencoding = "2.1"

# Servo dependencies
euclid = "0.22"
webrender_api = "0.62"
webrender = "0.62"
gleam = "0.15"

# Window management for Servo
winit = "0.29"
raw-window-handle = "0.6"

