[package]
name = "tech-browser"
version = "1.0.0"
description = "TECH-BROWSER - Ultra-modern web browser with privacy and performance"
authors = ["TECH-BROWSER Team"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tech_browser_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.2", features = [] }

[dependencies]
# Tauri core
tauri = { version = "2.2", features = ["protocol-asset"] }
tauri-plugin-opener = "2.2"
tauri-plugin-window-state = "2.2"

# Serialization
serde = { version = "1", features = ["derive"] }
serde_json = "1"

# Core dependencies only for Phase 1
url = "2.5"
tokio = { version = "1", features = ["full"] }
log = "0.4"
env_logger = "0.10"
uuid = { version = "1.0", features = ["v4"] }
urlencoding = "2.1"

