/**
 * @license React
 * react-dom-test-utils.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
(function(){'use strict';(function(f,q){"object"===typeof exports&&"undefined"!==typeof module?q(exports,require("react-dom")):"function"===typeof define&&define.amd?define(["exports","react","react-dom"],q):(f=f||self,q(f.ReactTestUtils={},f.React,f.ReactDOM))})(this,function(f,q,C){function K(a){var b=a,c=a;if(a.alternate)for(;b.return;)b=b.return;else{a=b;do b=a,0!==(b.flags&4098)&&(c=b.return),a=b.return;while(a)}return 3===b.tag?c:null}function L(a){if(K(a)!==a)throw Error("Unable to find node on an unmounted component.");
}function V(a){var b=a.alternate;if(!b){b=K(a);if(null===b)throw Error("Unable to find node on an unmounted component.");return b!==a?null:a}for(var c=a,d=b;;){var g=c.return;if(null===g)break;var h=g.alternate;if(null===h){d=g.return;if(null!==d){c=d;continue}break}if(g.child===h.child){for(h=g.child;h;){if(h===c)return L(g),a;if(h===d)return L(g),b;h=h.sibling}throw Error("Unable to find node on an unmounted component.");}if(c.return!==d.return)c=g,d=h;else{for(var e=!1,m=g.child;m;){if(m===c){e=
!0;c=g;d=h;break}if(m===d){e=!0;d=g;c=h;break}m=m.sibling}if(!e){for(m=h.child;m;){if(m===c){e=!0;c=h;d=g;break}if(m===d){e=!0;d=h;c=g;break}m=m.sibling}if(!e)throw Error("Child was not found in either parent set. This indicates a bug in React related to the return pointer. Please file an issue.");}}if(c.alternate!==d)throw Error("Return fibers should always be each others' alternates. This error is likely caused by a bug in React. Please file an issue.");}if(3!==c.tag)throw Error("Unable to find node on an unmounted component.");
return c.stateNode.current===c?a:b}function D(a){var b=a.keyCode;"charCode"in a?(a=a.charCode,0===a&&13===b&&(a=13)):a=b;10===a&&(a=13);return 32<=a||13===a?a:0}function x(){return!0}function M(){return!1}function n(a){function b(c,b,g,h,e){this._reactName=c;this._targetInst=g;this.type=b;this.nativeEvent=h;this.target=e;this.currentTarget=null;for(var d in a)a.hasOwnProperty(d)&&(c=a[d],this[d]=c?c(h):h[d]);this.isDefaultPrevented=(null!=h.defaultPrevented?h.defaultPrevented:!1===h.returnValue)?
x:M;this.isPropagationStopped=M;return this}k(b.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():"unknown"!==typeof a.returnValue&&(a.returnValue=!1),this.isDefaultPrevented=x)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():"unknown"!==typeof a.cancelBubble&&(a.cancelBubble=!0),this.isPropagationStopped=x)},persist:function(){},isPersistent:x});return b}function W(a){var b=this.nativeEvent;
return b.getModifierState?b.getModifierState(a):(a=X[a])?!!b[a]:!1}function E(a){return W}function Y(a,b,c,d,g,h,e,f,k){v=!1;y=null;Z.apply(aa,arguments)}function ba(a,b,c,d,g,h,e,f,k){Y.apply(this,arguments);if(v){if(v){var m=y;v=!1;y=null}else throw Error("clearCaughtError was called but no error was captured. This error is likely caused by a bug in React. Please file an issue.");z||(z=!0,F=m)}}function ca(a){}function da(a,b){if(!a)return[];a=V(a);if(!a)return[];for(var c=a,d=[];;){if(5===c.tag||
6===c.tag||1===c.tag||0===c.tag){var g=c.stateNode;b(g)&&d.push(g)}if(c.child)c.child.return=c,c=c.child;else{if(c===a)return d;for(;!c.sibling;){if(!c.return||c.return===a)return d;c=c.return}c.sibling.return=c.return;c=c.sibling}}}function t(a,b){if(a&&!a._reactInternals){var c=String(a);a=G(a)?"an array":a&&1===a.nodeType&&a.tagName?"a DOM node":"[object Object]"===c?"object with keys {"+Object.keys(a).join(", ")+"}":c;throw Error(b+"(...): the first argument must be a React class instance. Instead received: "+
(a+"."));}}function A(a){return!(!a||1!==a.nodeType||!a.tagName)}function H(a){return A(a)?!1:null!=a&&"function"===typeof a.render&&"function"===typeof a.setState}function N(a,b){return H(a)?a._reactInternals.type===b:!1}function B(a,b){t(a,"findAllInRenderedTree");return a?da(a._reactInternals,b):[]}function O(a,b){t(a,"scryRenderedDOMComponentsWithClass");return B(a,function(a){if(A(a)){var c=a.className;"string"!==typeof c&&(c=a.getAttribute("class")||"");var g=c.split(/\s+/);if(!G(b)){if(void 0===
b)throw Error("TestUtils.scryRenderedDOMComponentsWithClass expects a className as a second argument.");b=b.split(/\s+/)}return b.every(function(a){return-1!==g.indexOf(a)})}return!1})}function P(a,b){t(a,"scryRenderedDOMComponentsWithTag");return B(a,function(a){return A(a)&&a.tagName.toUpperCase()===b.toUpperCase()})}function Q(a,b){t(a,"scryRenderedComponentsWithType");return B(a,function(a){return N(a,b)})}function R(a,b,c){var d=a.type||"unknown-event";a.currentTarget=ea(c);ba(d,b,void 0,a);
a.currentTarget=null}function S(a,b,c){for(var d=[];a;){d.push(a);do a=a.return;while(a&&5!==a.tag);a=a?a:null}for(a=d.length;0<a--;)b(d[a],"captured",c);for(a=0;a<d.length;a++)b(d[a],"bubbled",c)}function T(a,b){var c=a.stateNode;if(!c)return null;var d=fa(c);if(!d)return null;c=d[b];a:switch(b){case "onClick":case "onClickCapture":case "onDoubleClick":case "onDoubleClickCapture":case "onMouseDown":case "onMouseDownCapture":case "onMouseMove":case "onMouseMoveCapture":case "onMouseUp":case "onMouseUpCapture":case "onMouseEnter":(d=
!d.disabled)||(a=a.type,d=!("button"===a||"input"===a||"select"===a||"textarea"===a));a=!d;break a;default:a=!1}if(a)return null;if(c&&"function"!==typeof c)throw Error("Expected `"+b+"` listener to be a function, instead got a value of `"+typeof c+"` type.");return c}function ha(a,b,c){a&&c&&c._reactName&&(b=T(a,c._reactName))&&(null==c._dispatchListeners&&(c._dispatchListeners=[]),null==c._dispatchInstances&&(c._dispatchInstances=[]),c._dispatchListeners.push(b),c._dispatchInstances.push(a))}function ia(a,
b,c){var d=c._reactName;"captured"===b&&(d+="Capture");if(b=T(a,d))null==c._dispatchListeners&&(c._dispatchListeners=[]),null==c._dispatchInstances&&(c._dispatchInstances=[]),c._dispatchListeners.push(b),c._dispatchInstances.push(a)}function ja(a){return function(b,c){if(q.isValidElement(b))throw Error("TestUtils.Simulate expected a DOM node as the first argument but received a React element. Pass the DOM node you wish to simulate the event on instead. Note that TestUtils.Simulate will not work if you are using shallow rendering.");
if(H(b))throw Error("TestUtils.Simulate expected a DOM node as the first argument but received a component instance. Pass the DOM node you wish to simulate the event on instead.");var d="on"+a[0].toUpperCase()+a.slice(1),g=new ca;g.target=b;g.type=a.toLowerCase();var f=ka(b),e=new la(d,g.type,f,g,b);e.persist();k(e,c);ma.has(a)?e&&e._reactName&&ha(e._targetInst,null,e):e&&e._reactName&&S(e._targetInst,ia,e);C.unstable_batchedUpdates(function(){na(b);if(e){var a=e._dispatchListeners,c=e._dispatchInstances;
if(G(a))for(var d=0;d<a.length&&!e.isPropagationStopped();d++)R(e,a[d],c[d]);else a&&R(e,a,c);e._dispatchListeners=null;e._dispatchInstances=null;e.isPersistent()||e.constructor.release(e)}if(z)throw a=F,z=!1,F=null,a;});oa()}}var k=Object.assign,r={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(a){return a.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},la=n(r),u=k({},r,{view:0,detail:0});n(u);var I,J,w,l=k({},u,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,
altKey:0,metaKey:0,getModifierState:E,button:0,buttons:0,relatedTarget:function(a){return void 0===a.relatedTarget?a.fromElement===a.srcElement?a.toElement:a.fromElement:a.relatedTarget},movementX:function(a){if("movementX"in a)return a.movementX;a!==w&&(w&&"mousemove"===a.type?(I=a.screenX-w.screenX,J=a.screenY-w.screenY):J=I=0,w=a);return I},movementY:function(a){return"movementY"in a?a.movementY:J}});n(l);var p=k({},l,{dataTransfer:0});n(p);p=k({},u,{relatedTarget:0});n(p);p=k({},r,{animationName:0,
elapsedTime:0,pseudoElement:0});n(p);p=k({},r,{clipboardData:function(a){return"clipboardData"in a?a.clipboardData:window.clipboardData}});n(p);p=k({},r,{data:0});n(p);var pa={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qa={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",
33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},X={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};p=k({},u,{key:function(a){if(a.key){var b=pa[a.key]||a.key;if("Unidentified"!==b)return b}return"keypress"===a.type?(a=D(a),13===a?"Enter":String.fromCharCode(a)):
"keydown"===a.type||"keyup"===a.type?qa[a.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:E,charCode:function(a){return"keypress"===a.type?D(a):0},keyCode:function(a){return"keydown"===a.type||"keyup"===a.type?a.keyCode:0},which:function(a){return"keypress"===a.type?D(a):"keydown"===a.type||"keyup"===a.type?a.keyCode:0}});n(p);p=k({},l,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,
isPrimary:0});n(p);u=k({},u,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:E});n(u);r=k({},r,{propertyName:0,elapsedTime:0,pseudoElement:0});n(r);l=k({},l,{deltaX:function(a){return"deltaX"in a?a.deltaX:"wheelDeltaX"in a?-a.wheelDeltaX:0},deltaY:function(a){return"deltaY"in a?a.deltaY:"wheelDeltaY"in a?-a.wheelDeltaY:"wheelDelta"in a?-a.wheelDelta:0},deltaZ:0,deltaMode:0});n(l);var Z=function(a,b,c,d,f,h,e,k,l){var g=Array.prototype.slice.call(arguments,
3);try{b.apply(c,g)}catch(ra){this.onError(ra)}},v=!1,y=null,z=!1,F=null,aa={onError:function(a){v=!0;y=a}},G=Array.isArray;l=C.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events;var ka=l[0],ea=l[1],fa=l[2],na=l[3],oa=l[4];l=q.unstable_act;var U={},ma=new Set(["mouseEnter","mouseLeave","pointerEnter","pointerLeave"]),sa="blur cancel click close contextMenu copy cut auxClick doubleClick dragEnd dragStart drop focus input invalid keyDown keyPress keyUp mouseDown mouseUp paste pause play pointerCancel pointerDown pointerUp rateChange reset resize seeked submit touchCancel touchEnd touchStart volumeChange drag dragEnter dragExit dragLeave dragOver mouseMove mouseOut mouseOver pointerMove pointerOut pointerOver scroll toggle touchMove wheel abort animationEnd animationIteration animationStart canPlay canPlayThrough durationChange emptied encrypted ended error gotPointerCapture load loadedData loadedMetadata loadStart lostPointerCapture playing progress seeking stalled suspend timeUpdate transitionEnd waiting mouseEnter mouseLeave pointerEnter pointerLeave change select beforeInput compositionEnd compositionStart compositionUpdate".split(" ");
(function(){sa.forEach(function(a){U[a]=ja(a)})})();f.Simulate=U;f.act=l;f.findAllInRenderedTree=B;f.findRenderedComponentWithType=function(a,b){t(a,"findRenderedComponentWithType");a=Q(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for componentType:"+b);return a[0]};f.findRenderedDOMComponentWithClass=function(a,b){t(a,"findRenderedDOMComponentWithClass");a=O(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for class:"+
b);return a[0]};f.findRenderedDOMComponentWithTag=function(a,b){t(a,"findRenderedDOMComponentWithTag");a=P(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for tag:"+b);return a[0]};f.isCompositeComponent=H;f.isCompositeComponentWithType=N;f.isDOMComponent=A;f.isDOMComponentElement=function(a){return!!(a&&q.isValidElement(a)&&a.tagName)};f.isElement=function(a){return q.isValidElement(a)};f.isElementOfType=function(a,b){return q.isValidElement(a)&&a.type===b};
f.mockComponent=function(a,b){b=b||a.mockTagName||"div";a.prototype.render.mockImplementation(function(){return q.createElement(b,null,this.props.children)});return this};f.nativeTouchData=function(a,b){return{touches:[{pageX:a,pageY:b}]}};f.renderIntoDocument=function(a){var b=document.createElement("div");return C.render(a,b)};f.scryRenderedComponentsWithType=Q;f.scryRenderedDOMComponentsWithClass=O;f.scryRenderedDOMComponentsWithTag=P;f.traverseTwoPhase=S});
})();
