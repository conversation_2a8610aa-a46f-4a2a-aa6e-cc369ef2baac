[package]
name = "tech-browser"
version = "0.1.0"
edition = "2021"
authors = ["TECH-BROWSER Team"]
description = "Navigateur web ultra moderne avec performance maximale et vie privée intraçable"

[dependencies]
# Wry - WebView moderne pour navigateur autonome (utilise WebView2 sur Windows)
wry = "0.51"
tao = "0.33"  # Gestionnaire de fenêtres pour wry

# egui - Interface native moderne
eframe = "0.29"
egui = "0.29"
urlencoding = "2.1"
rand = "0.8"

# Dépendances existantes
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
log = "0.4"
env_logger = "0.10"
dirs = "5.0"
toml = "0.8"

[build-dependencies]
winres = "0.1"

[target.'cfg(windows)'.build-dependencies]
winapi = { version = "0.3", features = ["winnt"] }

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "wingdi", "wincon", "libloaderapi", "dwmapi"] }

[[bin]]
name = "tech-browser"
path = "src/main.rs"

[[bin]]
name = "tech-browser-egui"
path = "src/main_egui.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
